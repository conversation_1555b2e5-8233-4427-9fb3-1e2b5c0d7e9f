local BattleService = {
    { 23900, "CLMatchReq", "LCMatchRsp", "CS", "MatchBattle", "user battle match" },
    { 23901, "", "LCMatchSuccessNotify", "SC", "NotifyMatchResult","匹配结果推送"},
    { 23902, "CLRoundBattleStartReq", "LCRoundBattleStartResp", "CS", "RoundBattleStart", "user round battle start" },
    { 23903, "", "LCRoundStartNotify", "SC", "NotifyRoundStart","新回合开始"},
    { 23904, "CLReadyReq", "LCReadyRsp", "CS", "BattleReady", "user ready battle" },
    { 23905, "", "LCRoundBattleStartNotify", "SC", "NotifyBattleStart","战斗开始"},
    { 23906, "CLRoundBattleEndReq", "LCRoundBattleEndResp", "CS", "RoundBattleEnd", "user end battle" },
    { 23907, "", "LCRoundBattleEndNotify", "SC", "NotifyRoundBattleEnd","回合战斗结束"},
    { 23908, "CLSelectBufferReq", "LCSelectBufferResp", "CS", "SelectBuffer", "user select buffer" },
    { 23909, "CLMergeReq", "LCMergeRsp", "CS", "MergeHero", "user merge hero" },
    { 23910, "", "LCBattleEndNotify", "SC", "NotifyBattleEnd","整场战斗结束"},
    { 23911, "CLClaimAdRewardReq", "LCClaimAdRewardRsp", "CS", "ClaimAdReward","claim ad reward"},
    { 23912, "CLLeaveBattleReq", "LCLeaveBattleRsp", "CS", "LeaveBattle","leave battle"},
}

return BattleService
