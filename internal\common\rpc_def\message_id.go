/**************************************************
 **这个文件是跟随proto协议一起自动生成的，不可手动修改！
 **************************************************/
package rpc_def

import "strconv"

const (
	//====================PublicMessage 消息ID ====================

	//====================BattleProto 消息ID ====================

	//====================CLProtocol 消息ID ====================
	CL_LOGIN_REQ                           = 1000 //客户端登录 Logic 服请求 &#64;ID=1000
	CL_PlayerData_REQ                      = 1001 //玩家数据请求 &#64;ID=1001
	CLHeartBeat                            = 1002 //客户端到服务器的 heartbeat    消息 的 id 必须是固定的 1002 &#64;ID=1002
	CLUpdateServerTime                     = 1003 //请求服务器的时间 &#64;ID=1003
	CLGmReq                                = 1004 //gm 指令 &#64;ID=1004
	CL_PlayerData_LoginOut                 = 1005 //玩家注销登出 &#64;ID=1005
	CLUseItemReq                           = 1006 //物品使用 &#64;ID=1006
	CLFuntionUnLockReq                     = 1007 //同步功能解锁 &#64;ID=1007
	CLMailAllListReq                       = 1008 //请求邮件信息 &#64;ID=1008
	CLReadMailReq                          = 1009 //玩家读取一封邮件的 &#64;ID=1009
	CLReceiveMailReq                       = 1010 //玩家领取东西 &#64;ID=1010
	CLReceiveAllMailReq                    = 1011 //玩家领取所有邮件 &#64;ID=1011
	CLDelMailReq                           = 1012 //玩家删除邮件 &#64;ID=1012
	CLDelAllReadMailReq                    = 1013 //一键删除邮件 &#64;ID=1013
	CLMailQuestionAwardReq                 = 1014 //问卷奖励邮件 &#64;ID=1014
	CLChangeNameReq                        = 1015 //玩家修改名字 &#64;ID=1015
	CLChangeSignReq                        = 1016 //修改个性签名 &#64;ID=1016
	CLChangeGenderReq                      = 1017 //修改玩家性别 &#64;ID=1017
	CLNewGuideStepInfoReq                  = 1018 //新手的下一步操作信息 &#64;ID=1018
	CLNewGuideClientStartReq               = 1019 //新手客户端触发的新手信息 &#64;ID=1019
	CLClientTriggerGuideReq                = 1020 //客户端触发新手引导组 &#64;ID=1020
	CLGuideStepFinishReq                   = 1021 //引导步骤完成 &#64;ID=1021
	CLGuideGroupFinishReq                  = 1022 //强制引导组完成(非正常情况调用) &#64;ID=1022
	CLSignInInfoRequest                    = 1023 //请求签到消息 &#64;ID=1023
	CLSignInRewardRequest                  = 1024 //领取签到奖励信息 &#64;ID=1024
	CLSevenDaySignInRewardRequest          = 1025 //自动领取七日签到奖励信息 &#64;ID=1025
	CLSignMonthRewardRequest               = 1026 //一键领取签到奖励信息 &#64;ID=1026
	CLSyncSettingReq                       = 1027 //向服务端同步设置数据 &#64;ID=1027
	CLAllFriendInvitationReq               = 1028 //同步  每次打开界面在同步一下 &#64;ID=1028
	CLFriendRasinRewardReq                 = 1029 //领取拉新奖励 &#64;ID=1029
	CLFriendGrowthQuestRewardReq           = 1030 //打开对应玩家任务 &#64;ID=1030
	CLFriendGrowthQuestReceiveRewardReq    = 1031 //领取对应玩家任务奖励 &#64;ID=1031
	CLFriendGrowthQuestReceiveAllRewardReq = 1032 //领取对应玩家所有任务奖励 &#64;ID=1032
	CLSharePosterReq                       = 1033 //分享 &#64;ID=1033
	CLEnterInvitationCodeReq               = 1034 //分享 &#64;ID=1034
	CLFinishCommonExpBoxData               = 1035 //完成阶段宝箱 &#64;ID=1035
	CLQuestReq                             = 1036 //打开问卷页面 &#64;ID=1036
	CLQuestAwardToMailReq                  = 1037 //问卷奖励邮件 &#64;ID=1037
	CLPlayerOtherInfo                      = 1038 //请求其他玩家信息 &#64;ID=1038
	CLGuildCreate                          = 1039 //创建公会 &#64;ID=1039
	CLGuildMain                            = 1040 //主按钮请求 &#64;ID=1040
	CLGuildFastJoin                        = 1041 //一键加入 &#64;ID=1041
	CLGuildSearch                          = 1042 //公会搜索 &#64;ID=1042
	CLGuildApply                           = 1043 //公会申请 &#64;ID=1043
	CLGuildHall                            = 1044 //公会大厅 &#64;ID=1044
	CLGuildTech                            = 1045 //公会科技 &#64;ID=1045
	CLGuildTechLevelup                     = 1046 //公会科技强化 &#64;ID=1046
	CLGuildTechReset                       = 1047 //公会科技重置 &#64;ID=1047
	CLGuildShop                            = 1048 //公会商店 &#64;ID=1048
	CLGuildShopBuy                         = 1049 //公会商店购买 &#64;ID=1049
	CLGuildEdit                            = 1050 //修改公会信息 &#64;ID=1050
	CLGuildApplyMgrList                    = 1051 //公会申请列表 &#64;ID=1051
	CLGuildMemberMgrList                   = 1052 //公会成员管理 &#64;ID=1052
	CLGuildApplyMgr                        = 1053 //公会申请列表操作 &#64;ID=1053
	CLGuildMemberMgr                       = 1054 //公会成员管理 &#64;ID=1054
	CLGuildQuit                            = 1055 //退出公会 &#64;ID=1055
	CLGuildDismiss                         = 1056 //解散公会 &#64;ID=1056
	CLGuildImpeach                         = 1057 //弹劾会长 &#64;ID=1057
	CLGuildSendWorldInvite                 = 1058 //发送世界邀请 &#64;ID=1058
	CLGuildSendPlayerInvite                = 1059 //发送私聊邀请 &#64;ID=1059
	CLGuildInviteJoin                      = 1060 //公会邀请加入 &#64;ID=1060
	CLGuildDonate                          = 1061 //公会捐献 &#64;ID=1061
	CLGuildRank                            = 1062 //公会排行榜 &#64;ID=1062
	CLGuildLog                             = 1063 //公会日志 &#64;ID=1063
	CLGuildBargainingInfo                  = 1064 //公会砍价礼包点击 &#64;ID=1064
	CLGuildBargainingNotice                = 1065 //公会砍价礼包提醒砍价 &#64;ID=1065
	CLGuildBargaining                      = 1066 //公会砍价礼包砍价 &#64;ID=1066
	CLGuildBargainingBuy                   = 1067 //公会砍价礼包购买 &#64;ID=1067
	CLGuildBossEnter                       = 1068 //公会 BOSS 进入 &#64;ID=1068
	CLGuildBossAtkBegin                    = 1069 //公会 BOSS 挑战 &#64;ID=1069
	CLGuildBossSweep                       = 1070 //公会 BOSS 扫荡 &#64;ID=1070
	CLGuildBossBuyCount                    = 1071 //公会 BOSS 次数购买 &#64;ID=1071
	CLGuildBossRank                        = 1072 //公会 BOSS 总排行 &#64;ID=1072
	CLSendChatInfo                         = 1073 // 聊天 &#64;ID=1073
	CLPrivateRed                           = 1074 // 聊天去掉私聊红点 &#64;ID=1074
	CLWorldMsgGet                          = 1075 // 聊天拉取 &#64;ID=1075
	CLRecommendFriendReq                   = 1076 //请求推荐好友 &#64;ID=1076
	CLSyncAllFriendsReq                    = 1077 //请求好友信息 (会同步好友列表，好友申请列表和黑名单) &#64;ID=1077
	CLAllGiftReq                           = 1078 //请求好友礼物 &#64;ID=1078
	CLAddOneFriendReq                      = 1079 //添加好友 &#64;ID=1079
	CLAgreeFriendReq                       = 1080 //同意好友 &#64;ID=1080
	CLRefuseFriendReq                      = 1081 //拒绝好友 &#64;ID=1081
	CLDelOneFriendReq                      = 1082 //删除一个好友 &#64;ID=1082
	CLblacklistOperationReq                = 1083 //操作黑名单 &#64;ID=1083
	CLQuestRed                             = 1084 // 问卷去掉红点 &#64;ID=1084
	CLBILog                                = 1085 //BI &#64;ID=1085
	CLClientAdReceive                      = 1086 //客户端领取广告奖励 &#64;ID=1086
	CLGuildTopList                         = 1087 //公会 TOP 请求 &#64;ID=1087
	CLHeadIconReq                          = 1088 // 请求头像信息 &#64;ID=1088
	CLReplaceHeadIconReq                   = 1089 //替换头像 &#64;ID=1089
	CLUnlockHeadIcon                       = 1090 //手动解锁头像，前提是已拥有。 &#64;ID=1090
	CLHeadFrame                            = 1091 // 请求头像框信息 &#64;ID=1091
	CLReplaceHeadFrame                     = 1092 //替换头像框 &#64;ID=1092
	CLUnlockHeadFrame                      = 1093 //手动解锁头像框，前提是已拥有。 &#64;ID=1093
	CLGiftBuy                              = 1094 //======================商城购买礼包========================================= &#64;ID=1094
	CLTimeGiftBuy                          = 1095 //======================限时商城购买礼包========================================= &#64;ID=1095
	CLSevenSignInGetData                   = 1096 // 请求七日签到信息 &#64;ID=1096
	CLSevenSignInGetAward                  = 1097 // 请求领取七日签到奖励 &#64;ID=1097
	CLDailySignInGetData                   = 1098 // 请求每日签到信息 &#64;ID=1098
	CLDailySignInGetAward                  = 1099 // 请求领取每日签到奖励 &#64;ID=1099
	CLDailySignInGetAccruedAward           = 1100 // 请求领取每日签到累签奖励 &#64;ID=1100
	CLFirstChargeGetReward                 = 1101 //======================首冲礼包========================================= &#64;ID=1101
	CLBuyFirstChargeGift                   = 1102 //购买首冲礼包 &#64;ID=1102
	CLTopupRebateGetData                   = 1103 //请求充值返利数据 &#64;ID=1103
	CLTopupRebateGetAward                  = 1104 //请求领取充值返利奖励 &#64;ID=1104
	CLMonthlyCardGetData                   = 1105 //请求月卡数据 &#64;ID=1105
	CLMonthlyCardBuyCard                   = 1106 //请求购买月卡数据 &#64;ID=1106
	CLMonthlyCardNewGetData                = 1107 //请求月卡2.0数据 &#64;ID=1107
	CLMonthlyCardNewGetExtraReward         = 1108 //请求月卡2.0额外奖励 &#64;ID=1108
	CLGradedFundGetData                    = 1109 //请求充值返利数据 &#64;ID=1109
	CLGradedFundBuyFund                    = 1110 //请求购买等级基金 &#64;ID=1110
	CLGradedFundGetComWeal                 = 1111 //请求领取普通等级基金  &#64;ID=1111
	CLGradedFundGetSuperWeal               = 1112 //请求领取超级等级基金 &#64;ID=1112
	CLMissionSubmit                        = 1113 //请求提交任务 （领取任务奖励） &#64;ID=1113
	CLMissionSubmitById                    = 1114 //请求提交任务 （领取任务奖励） &#64;ID=1114
	CLPaymentPreRequestReq                 = 1115 // 付款预请求 &#64;ID=1115
	CLGetActivityReward                    = 1116 // 领取活动奖励 &#64;ID=1116
	CLGetSevenDayActivityData              = 1117 // 请求七日活动数据 &#64;ID=1117
	CLDeleteAccount                        = 1118 // 删除账号 &#64;ID=1118
	CLRedeemCodeRewardReq                  = 1119 // 领取礼包码对应道具奖励 &#64;ID=1119
	CLGachaBonusGetData                    = 1120 //请求千抽信息 &#64;ID=1120
	CLGachaBonusGetAward                   = 1121 //请求千抽奖励 &#64;ID=1121
	CLGetFuncPrevReward                    = 1122 // 领取功能预告对应奖励 &#64;ID=1122
	CLGetQuestionReward                    = 1123 // 领取问卷奖励 &#64;ID=1123
	CLGachaWheelReward                     = 1124 // 轮盘抽奖 &#64;ID=1124
	CL_TowerMain                           = 1125 //首页 &#64;ID=1125
	CL_TowerStart                          = 1126 //爬塔开始 &#64;ID=1126
	CLTotalRechargeGetReward               = 1127 //首页 &#64;ID=1127
	CLInviteTaskList                       = 1128 //邀请任务列表 &#64;ID=1128
	CLInviteTaskShare                      = 1129 //邀请分享 &#64;ID=1129
	CLInviteTaskGetReward                  = 1130 //领取邀请奖励 &#64;ID=1130
	CLArenaGetData                         = 1131 //竞技场获取数据 &#64;ID=1131
	CLArenaReqChallenge                    = 1132 //竞技场请求挑战对手 &#64;ID=1132
	CLOpenPowerBuyingReq                   = 1133 // 请求打开体力界面 &#64;ID=1133
	CLPowerBuyingReq                       = 1134 // 请求购买体力 &#64;ID=1134
	CLPowerRewardReq                       = 1135 // 请求打开食堂餐食界面 空字段 &#64;ID=1135
	CLAllPowerRewardReq                    = 1136 // 请求一键领取食堂餐食 空字段 &#64;ID=1136
	CLOnePowerRewardReq                    = 1137 // 请求领取指定体力包 &#64;ID=1137
	CLHeavenlyDaoInfoReq                   = 1138 // 请求当前信息 &#64;ID=1138
	CLHeavenlyDaoPromoteReq                = 1139 // 请求晋升 &#64;ID=1139
	CLWeekCardReq                          = 1140 //周卡领取 &#64;ID=1140
	CLOpenHookReq                          = 1141 // 请求打开挂机奖励页面 &#64;ID=1141
	CLGetHookRewardReq                     = 1142 // 点击领取发送领取奖励请求 &#64;ID=1142
	CLGetHookExtraRewardReq                = 1143 // 点击领取后发送领取额外奖励请求  &#64;ID=1143
	CLTipOffReq                            = 1144 // 举报信息 &#64;ID=1144
	CLSendSingleGiftReq                    = 1145 //赠送礼物 &#64;ID=1145
	CLReciveSingleGiftReq                  = 1146 //领取单个礼物 &#64;ID=1146
	CLSendAllGiftReq                       = 1147 //一键赠送 &#64;ID=1147
	CLReciveAllGiftReq                     = 1148 //一键领取 &#64;ID=1148
	CLHeroListReq                          = 1149 //英雄列表 &#64;ID=1149
	CLHeroLevelUpReq                       = 1150 //英雄升级 &#64;ID=1150
	CLHeroAwakeLevelUpReq                  = 1151 //英雄觉醒等级升级 &#64;ID=1151
	CLLineupListReq                        = 1152 //获取所有阵容信息 &#64;ID=1152
	CLLineupUnlockSlot                     = 1153 //阵容槽位解锁 &#64;ID=1153
	CLLineupSwitchReq                      = 1154 //切换阵容 &#64;ID=1154
	CLLineupSetReq                         = 1155 //英雄上阵 &#64;ID=1155
	CLLineupRenameReq                      = 1156 //阵容重命名 &#64;ID=1156
	CLSeasonBuffReq                        = 1157 //赛季buff信息 &#64;ID=1157
	CLMatchReq                             = 1158 //开始匹配 &#64;ID=1158
	CLRoundBattleStartReq                  = 1159 //请求开始回合战斗 &#64;ID=1159
	CLSelectBufferReq                      = 1160 //buffer 选择 &#64;ID=1160
	CLEnterSceneReq                        = 1161 //进入战场 &#64;ID=1161
	CLMergeReq                             = 1162 //英雄合成 &#64;ID=1162
	CLReadyReq                             = 1163 //准备 &#64;ID=1163
	CLRoundBattleEndReq                    = 1164 //战斗结束 &#64;ID=1164
	CLLeaveBattleReq                       = 1165 //离开战斗 &#64;ID=1165
	CLClaimAdRewardReq                     = 1166 // 结算额外广告奖励 &#64;ID=1166
	CLClaimSeasonRewardReq                 = 1167 // 请求领取奖励 &#64;ID=1167
	CLSeasonInfoReq                        = 1168 // 请求赛季相关信息 &#64;ID=1168

	//====================DBProtocol 消息ID ====================

	//====================GLProtocol 消息ID ====================
	GL_Test_Res                     = 3000 //Test &#64;ID=3000
	Gl_GlobalMailNeedReload_Res     = 3001 //通知 lobyy玩家是否需要重新laod 全局邮件 &#64;ID=3001
	Gl_NoticeSyncFriend_Res         = 3002 // 通知去请求好友信息 &#64;ID=3002
	Gl_GlobalKickPlayer_Res         = 3003 // 踢人信息 &#64;ID=3003
	GL_GlobalToLobbyRunJS_Res       = 3004 // 通知Lobby执行JS脚本信息 &#64;ID=3004
	Gl_GlobalGetAllActivityInfo_Res = 3005 //通知 lobby 服务器状态信息 &#64;ID=3005
	Gl_GlobalGetActivityInfo_Res    = 3006 //通知 lobby 服务器状态信息 &#64;ID=3006
	GL_CDKeyUse_Res                 = 3007 //请求兑换码兑换 &#64;ID=3007
	Gl_GlobalChatBanPlayer_Res      = 3008 // 通知禁言信息 &#64;ID=3008
	Gl_GlobalWeather_Res            = 3009 // 通知天气信息 &#64;ID=3009
	GL_GlobalBroadcastEvent_Res     = 3010 //全局广播事件信息GlobalToAllLobby &#64;ID=3010
	GM2PS_SendGlobalMail            = 3011 // 全局邮件系统发送给玩家系统的消息 &#64;ID=3011

	//====================LCProtocol 消息ID ====================
	LC_LOGIN_RET                       = 2000 //Logic 服返回返回客户端登录结果 &#64;ID=2000
	LCHeartBeat                        = 2001 //服务器到客户端的 heartbeat &#64;ID=2001
	LC_PlayerData_Sync                 = 2002 //Logic 同步玩家数据 &#64;ID=2002
	LC_PlayerData_Complate             = 2003 //通知客户端最后一个登录消息包 &#64;ID=2003
	LCNotice                           = 2004 //醒目提示 &#64;ID=2004
	LCLoginUpdateServerTime            = 2005 //服务器登陆返回的时间 &#64;ID=2005
	LCUpdateServerTime                 = 2006 //服务器时间返回普通的通知 &#64;ID=2006
	LCNotifyCrossDay                   = 2007 //服务器提醒客户端跨天 &#64;ID=2007
	LC_PlayerResource_Sync             = 2008 //Logic 同步玩家基础资源信息 &#64;ID=2008
	LCDropItemListRes                  = 2009 //掉落道具同步展示 &#64;ID=2009
	LCSceneDropItemListRes             = 2010 //场景里面里面掉落 &#64;ID=2010
	LCSyncRedDotData                   = 2011 //红点 &#64;ID=2011
	LCSyncBagItemListRst               = 2012 //背包物品同步 &#64;ID=2012
	LCAllItemList                      = 2013 //同步全部物品信息 &#64;ID=2013
	LCSingleItem                       = 2014 //同步单个物品信息 &#64;ID=2014
	LCAttributeLevelUp                 = 2015 //属性升级 回包 &#64;ID=2015
	LC_AddExp_RES                      = 2016 //增加经验返回 &#64;ID=2016
	LCOpenHook                         = 2017 //挂机面板回包 &#64;ID=2017
	LCReceiveHook                      = 2018 // 领取挂机奖励 (额外奖励也用这个) &#64;ID=2018
	LCReceiveHookSweep                 = 2019 //领取扫荡奖励 &#64;ID=2019
	LCMailListRes                      = 2020 //返回所有的邮件 &#64;ID=2020
	LCReadMailRes                      = 2021 //读取邮件返回 &#64;ID=2021
	LCReceiveMailRes                   = 2022 //领取奖励返回 &#64;ID=2022
	LCReceiveAllMailRes                = 2023 //玩家领取所有邮件 &#64;ID=2023
	LCDelMailRes                       = 2024 //删除一封邮件返回 &#64;ID=2024
	LCDelAllReadMailRes                = 2025 //一键删除返回 &#64;ID=2025
	LCMailQuestionAwardRes             = 2026 //问卷奖励邮件返回 &#64;ID=2026
	LCChangeNameRes                    = 2027 //改名 &#64;ID=2027
	LCChangeSignRst                    = 2028 //修改个性签名返回 &#64;ID=2028
	LCChangeGenderRst                  = 2029 //修改玩家性别返回 &#64;ID=2029
	LCCancellationRst                  = 2030 //申请销号返回 &#64;ID=2030
	LCInitNewGuideStepInfoRes          = 2031 //初始化新手的步骤信息 &#64;ID=2031
	LCNewGuideStepInfoRes              = 2032 //同步新手的步骤信息 &#64;ID=2032
	LCUpCurNewGuideStepInfoRes         = 2033 //更新当前的新手信息 &#64;ID=2033
	LCSyncNewGuideInfoRsp              = 2034 //新手引导信息同步 &#64;ID=2034
	LCSignInInfoResponse               = 2035 //签到信息返回 &#64;ID=2035
	LCSignInGetRewardResponse          = 2036 // 签到奖励信息确认 &#64;ID=2036
	LCSignMonthGetRewardResponse       = 2037 // 月签到一键领取签到奖励信息 &#64;ID=2037
	LCSignSevenRewardResponse          = 2038 // 七日签到领取奖励 &#64;ID=2038
	LCSystemShowErrorMessageBox        = 2039 //通知系统提示的 &#64;ID=2039
	LCSyncSettingRes                   = 2040 //向客户端同步返回设置数据 &#64;ID=2040
	LCSyncAllFriendInvitationRes       = 2041 //登录同步 &#64;ID=2041
	LCFriendInvitationMainRes          = 2042 //打开界面同步 &#64;ID=2042
	LCSyncFriendInvitationRes          = 2043 //同步单条奖励领取 &#64;ID=2043
	LCSyncFriendGrowthQuestRewardRes   = 2044 //社交分裂任务列表 &#64;ID=2044
	LCSyncFriendGrowthReceiveRewardRes = 2045 //同步社交分裂任务 &#64;ID=2045
	LCSyncSharePosterRes               = 2046 //分享成功 同步 &#64;ID=2046
	LCSyncEnterInvitationCodeRes       = 2047 //邀请码状态 &#64;ID=2047
	LCSyncChargeRes                    = 2048 //充值标记同步 &#64;ID=2048
	LC_MIDAS_DILIVERY_SUCCESS          = 2049 //米大师钻石充值、道具直购、IOS 订阅发货成功 [c#] &#64;ID=2049
	LC_MIDAS_DILIVERY_RepeatPurchase   = 2050 //米大师钻石充值、道具直购、IOS 订阅发货成功 [c#] &#64;ID=2050
	LC_MIDAS_DIAMOND_NTF               = 2051 //米大师钻石充值压测占位 &#64;ID=2051
	LC_MIDAS_ITEM_NTF                  = 2052 //米大师道具直购压测占位 &#64;ID=2052
	LCReConnectComplete                = 2053 //断线重连成功结束标识 &#64;ID=2053
	LCAFData                           = 2054 //AF 埋点相关数据 &#64;ID=2054
	LCCommonExpBoxData                 = 2055 //阶段宝箱相关数据 &#64;ID=2055
	LCFinishCommonExpBoxData           = 2056 //完成阶段宝箱 &#64;ID=2056
	LCPushActivityInfo                 = 2057 //======================活动开始========================================= &#64;ID=2057
	LCGetActivityReward                = 2058 //领取活动奖励 &#64;ID=2058
	LCGetSevenDayActivityData          = 2059 //开放中的七日活动数据 &#64;ID=2059
	LCServerTimeDailyFiveRes           = 2060 // 在线 5 点消息通知 &#64;ID=2060
	LCRankListRes                      = 2061 //排行榜操作 &#64;ID=2061
	LCRankRewardRes                    = 2062 //排行榜排行奖励操作 &#64;ID=2062
	LCClientAdData                     = 2063 //广告客户端上报数据 &#64;ID=2063
	LCSyncQuestList                    = 2064 //同步生效的问卷列表 &#64;ID=2064
	LCSyncSingleQuest                  = 2065 //同步单个问卷 &#64;ID=2065
	LCPlayerOtherInfo                  = 2066 //请求其他玩家信息 &#64;ID=2066
	LCGuildMain                        = 2067 //公会大厅主界面（已在公会中） &#64;ID=2067
	LCGuildRecommendList               = 2068 //公会推荐列表界面（不在公会时） &#64;ID=2068
	LCGuildSearch                      = 2069 //公会搜索 &#64;ID=2069
	LCGuildApply                       = 2070 //公会搜索 &#64;ID=2070
	LCGuildHall                        = 2071 //公会大厅 &#64;ID=2071
	LCGuildTech                        = 2072 //公会科技 &#64;ID=2072
	LCGuildTechLevelup                 = 2073 //公会科技强化 &#64;ID=2073
	LCGuildTechReset                   = 2074 //公会科技重置 &#64;ID=2074
	LCGuildEdit                        = 2075 //修改公会信息 &#64;ID=2075
	LCGuildApplyMgrList                = 2076 //公会申请列表 &#64;ID=2076
	LCGuildMemberMgrList               = 2077 //公会成员管理 &#64;ID=2077
	LCGuildApplyMgr                    = 2078 //公会申请列表操作 &#64;ID=2078
	LCGuildMemberMgr                   = 2079 //公会成员管理 &#64;ID=2079
	LCGuildQuit                        = 2080 //退出公会 &#64;ID=2080
	LCGuildDismiss                     = 2081 //解散公会 &#64;ID=2081
	LCGuildImpeach                     = 2082 //弹劾会长 &#64;ID=2082
	LCGuildSendWorldInvite             = 2083 //发送世界邀请 &#64;ID=2083
	LCGuildSendPlayerInvite            = 2084 //发送私聊邀请 &#64;ID=2084
	LCGuildInviteJoin                  = 2085 //公会邀请加入 &#64;ID=2085
	LCGuildDonate                      = 2086 //公会捐献 &#64;ID=2086
	LCGuildRank                        = 2087 //公会排行榜 &#64;ID=2087
	LCGuildLog                         = 2088 //公会日志 &#64;ID=2088
	LCGuildBargainingInfo              = 2089 //公会砍价礼包点击 &#64;ID=2089
	LCGuildBargainingNotice            = 2090 //公会砍价礼包提醒砍价 &#64;ID=2090
	LCGuildBargaining                  = 2091 //公会砍价礼包砍价 &#64;ID=2091
	LCGuildBargainingBuy               = 2092 //公会砍价礼包购买 &#64;ID=2092
	LC_Guild_Sync                      = 2093 //同步玩家公会 ID 数据 &#64;ID=2093
	LCGuildBossEnter                   = 2094 //公会 BOSS 进入 &#64;ID=2094
	LCGuildBossAtkBegin                = 2095 //公会 BOSS 挑战开始 &#64;ID=2095
	LCGuildBossSweep                   = 2096 //公会 BOSS 扫荡 &#64;ID=2096
	LCGuildBossBuyCount                = 2097 //公会 BOSS 次数购买 &#64;ID=2097
	LCGuildBossRank                    = 2098 //公会 BOSS 总排行 &#64;ID=2098
	LCGuildCreate                      = 2099 // 创建公会 &#64;ID=2099
	LCGuildShop                        = 2100 //公会商店 &#64;ID=2100
	LCGuildShopBuy                     = 2101 //公会商店购买 &#64;ID=2101
	LCSyncChatInfo                     = 2102 // 同步消息 &#64;ID=2102
	LCBroadcastGuideChatInfo           = 2103 // 广播协会消息 &#64;ID=2103
	LCBroadcastPrivateChatInfo         = 2104 // 私聊通知消息 &#64;ID=2104
	LCLoginGetAllPrivateChatInfo       = 2105 // 登录私聊 &#64;ID=2105
	LCLoginGetAllChatInfo              = 2106 // 登录世界 和协会 &#64;ID=2106
	LCSyncRecommendFriendRes           = 2107 //同步推荐好友 &#64;ID=2107
	LCSyncAllFriendsRes                = 2108 //同步所有好友信息 &#64;ID=2108
	LCBlackPlayerRes                   = 2109 // 黑名单列表 &#64;ID=2109
	LCSyncRequestAddOneFriendRes       = 2110 //申请列表 增加好友 &#64;ID=2110
	LCSyncAddFriendRes                 = 2111 // 同意 增加好友返回 &#64;ID=2111
	LCSyncRefuseFriendRes              = 2112 // 拒绝好友返回 &#64;ID=2112
	LCSyncDelOneFriendRes              = 2113 // 删除好友返回 &#64;ID=2113
	LCblacklistOperationRes            = 2114 // 操作黑名单 &#64;ID=2114
	LCSyncCreateFriendTime             = 2115 //同步好友创建时间 &#64;ID=2115
	LCSyncGiftInfoRes                  = 2116 //同步礼物信息 &#64;ID=2116
	LCSyncRecGiftInfoRes               = 2117 //同步收礼信息 &#64;ID=2117
	LCSyncSendGiftInfoRes              = 2118 //同步送礼信息 &#64;ID=2118
	LCShowRecGift                      = 2119 //领取礼物展示恭喜获得 &#64;ID=2119
	LCSyncSendApplyFriend              = 2120 //同步已发出申请的好友 &#64;ID=2120
	LCApplyFriendRes                   = 2121 // 点击好友申请返回 &#64;ID=2121
	LCCDKeyUse                         = 2122 //兑换码 &#64;ID=2122
	LCReNetReconnect                   = 2123 //重连成功后通知客户端重连成功 补充消息包用的 &#64;ID=2123
	LCGuildTopList                     = 2124 //公会 TOP 请求 &#64;ID=2124
	LCSyncHeadIconList                 = 2125 //同步头像列表 &#64;ID=2125
	LCSyncSingleHeadIcon               = 2126 //同步单个头像 &#64;ID=2126
	LCReplaceHeadIcon                  = 2127 //替换头像数据 &#64;ID=2127
	LCUnlockHeadIcon                   = 2128 //手动解锁头像，前提是已拥有。 &#64;ID=2128
	LCSyncHeadFrameList                = 2129 //同步头像框列表 &#64;ID=2129
	LCSyncSingleHeadFrame              = 2130 //同步单个头像框 &#64;ID=2130
	LCReplaceHeadFrame                 = 2131 //替换头像框 &#64;ID=2131
	LCUnlockHeadFrame                  = 2132 //解锁头像框 &#64;ID=2132
	LCGiftBuyRes                       = 2133 //======================商城购买礼包========================================= &#64;ID=2133
	LCShopGiftList                     = 2134 //登陆推送商城礼包数据 &#64;ID=2134
	LCTimeGiftBuyRes                   = 2135 //======================限时商城购买礼包========================================= &#64;ID=2135
	LCTimeShopGiftList                 = 2136 //登陆推送商城礼包数据 &#64;ID=2136
	LCSevenSignInGetDataRe             = 2137 // 返回七日签到信息 &#64;ID=2137
	LCSevenSignInGetAwardRe            = 2138 // 返回领取七日签到奖励 &#64;ID=2138
	LCDailySignInGetDataRe             = 2139 // 请求每日签到信息 &#64;ID=2139
	LCDailySignInGetAwardRe            = 2140 // 请求领取每日签到奖励 &#64;ID=2140
	LCDailySignInGetAccruedAwardRe     = 2141 // 请求领取每日签到累签奖励 &#64;ID=2141
	LCGetFristChargeRewardRe           = 2142 //领取首冲礼包奖励成功失败返回 &#64;ID=2142
	LCFirstChargeGetDataRe             = 2143 //首冲礼包数据同步消息 &#64;ID=2143
	LCBuyFirstChargeGiftRet            = 2144 //购买首冲礼包 &#64;ID=2144
	LCTopupRebateGetDataRe             = 2145 // 响应请求充值返利数据 &#64;ID=2145
	LCTopupRebateGetAwardRe            = 2146 // 响应领取充值返利奖励 &#64;ID=2146
	LCMonthlyCardGetDataRe             = 2147 // 响应请求月卡数据 &#64;ID=2147
	LCMonthlyCardBuyCardRe             = 2148 // 响应请求购买月卡 &#64;ID=2148
	LCMonthlyCardNewGetDataRe          = 2149 // 响应请求月卡2.0数据 &#64;ID=2149
	LCMonthlyCardNewGetExtraReward     = 2150 // 响应请求月卡2.0额外奖励 &#64;ID=2150
	LCGradedFundGetDataRe              = 2151 //响应请求等级基金数据 &#64;ID=2151
	LCGradedFundBuyFundRe              = 2152 //响应购买等级基金 &#64;ID=2152
	LCGradedFundGetComWealRe           = 2153 //响应领取普通等级基金  &#64;ID=2153
	LCGradedFundGetSuperWealRe         = 2154 //响应领取超级等级基金 &#64;ID=2154
	LCMissionGetData                   = 2155 //响应请求任务数据 &#64;ID=2155
	LCMissionSubmit                    = 2156 //响应提交任务 （领取任务奖励） &#64;ID=2156
	LCCommonAwardDisplay               = 2157 //======================奖励通用展示开始========================================== &#64;ID=2157
	LCPaymentPreRequestRe              = 2158 // 响应付款预请求 &#64;ID=2158
	LCPaymentRequestTestRe             = 2159 // 非SDK正式充值情况下，充值逻辑功能返回消息 &#64;ID=2159
	LCFunctionOpenPushAll              = 2160 //	登录开放列表下发 &#64;ID=2160
	LCFunctionOpenSync                 = 2161 //	功能开放同步前端 &#64;ID=2161
	LCDeleteAccount                    = 2162 // 删除账号 &#64;ID=2162
	LCRedeemCodeRewardRst              = 2163 // 返回使用礼包码结果 &#64;ID=2163
	LCGachaBonusGetDataRe              = 2164 // 返回千抽信息 &#64;ID=2164
	LCGachaBonusGetAwardRe             = 2165 // 返回领取千抽奖励 &#64;ID=2165
	LCFuncPrevPushAll                  = 2166 //	登录领取列表下发 &#64;ID=2166
	LCFuncPrevReward                   = 2167 // 领取解锁奖励结果 &#64;ID=2167
	LCGetQuestionReward                = 2168 // 领取问卷奖励 &#64;ID=2168
	LCSyncRewardQuestion               = 2169 // 登录推送已领取过奖励的问卷 &#64;ID=2169
	LCGachaWheelReward                 = 2170 // 轮盘抽奖 &#64;ID=2170
	LC_TowerMain                       = 2171 //爬塔首页 &#64;ID=2171
	LC_SyncTower                       = 2172 //爬塔同步整包 &#64;ID=2172
	LCTotalRechargeGetReward           = 2173 //首页 &#64;ID=2173
	LCTotalRechargeData                = 2174 //数据 &#64;ID=2174
	LCInviteTaskList                   = 2175 //邀请任务列表 &#64;ID=2175
	LCInviteTaskShare                  = 2176 //邀请分享 &#64;ID=2176
	LCInviteTaskGetReward              = 2177 //领取邀请奖励 &#64;ID=2177
	LCArenaGetData                     = 2178 //竞技场获取数据 &#64;ID=2178
	LCArenaChallengeResult             = 2179 //竞技场请求挑战对手 &#64;ID=2179
	LCOpenPowerBuyingRes               = 2180 // 响应打开体力界面 返回可购买次数 &#64;ID=2180
	LCPowerBuyingRes                   = 2181 // 响应体力购买  买完后剩余次数 &#64;ID=2181
	LCSyncPowerRewardList              = 2182 // 服务端响应可领取的体力包列表 &#64;ID=2182
	LCAllPowerRewardRes                = 2183 // 服务器响应领取体力包结果 &#64;ID=2183
	LCOnePowerRewardRes                = 2184 // 服务器响应领取指定体力包 &#64;ID=2184
	LCHeavenlyDaoInfoRes               = 2185 // 请求当前信息 &#64;ID=2185
	LCHeavenlyDaoPromoteRes            = 2186 // 晋升结果 &#64;ID=2186
	LCWeekCardDataRes                  = 2187 //周卡数据 &#64;ID=2187
	LCTipOffRes                        = 2188 // 举报信息反馈 &#64;ID=2188
	LCHeroListResp                     = 2189 //英雄列表 &#64;ID=2189
	LCHeroLevelUpRes                   = 2190 //英雄升级 &#64;ID=2190
	LCHeroAwakeLevelUpRes              = 2191 //英雄觉醒等级升级 &#64;ID=2191
	LCLineupListResp                   = 2192 //获取所有阵容信息 &#64;ID=2192
	LCLineupUnlockSlotRes              = 2193 //阵容槽位解锁 &#64;ID=2193
	LCLineupSwitchResp                 = 2194 //切换阵容 &#64;ID=2194
	LCLineupSetReq                     = 2195 //英雄上阵 &#64;ID=2195
	LCLineupRenameResp                 = 2196 //阵容重命名 &#64;ID=2196
	LCSeasonBuffRes                    = 2197 //赛季buff信息 &#64;ID=2197
	LCMatchRsp                         = 2198 //匹配返回 &#64;ID=2198
	LCMatchSuccessNotify               = 2199 //匹配成功通知 &#64;ID=2199
	LCRoundBattleStartResp             = 2200 //请求开始回合战斗 &#64;ID=2200
	LCRoundStartNotify                 = 2201 //新回合开始 &#64;ID=2201
	LCSelectBufferResp                 = 2202 //buffer 选择 &#64;ID=2202
	LCMergeRsp                         = 2203 //英雄合成 &#64;ID=2203
	LCReadyRsp                         = 2204 //准备返回 &#64;ID=2204
	LCRoundBattleEndResp               = 2205 //战斗结束 &#64;ID=2205
	LCRoundBattleStartNotify           = 2206 //战斗开始 &#64;ID=2206
	LCRoundBattleEndNotify             = 2207 //战斗结束 &#64;ID=2207
	LCBattleEndNotify                  = 2208 //整场战斗结束 &#64;ID=2208
	LCLeaveBattleRsp                   = 2209 //离开战斗 &#64;ID=2209
	LCClaimAdRewardRsp                 = 2210 // 结算额外广告奖励 &#64;ID=2210
	LCClaimSeasonRewardRsp             = 2211 // 请求领取奖励 &#64;ID=2211
	LCSeasonInfoRsp                    = 2212 // 请求赛季相关信息  &#64;ID=2212
	LCSeasonResetNotify                = 2213 // 服务器主动推送：赛季重置通知 &#64;ID=2213

	//====================LGProtocol 消息ID ====================
	LG_Test_Req                     = 4000 //Test &#64;ID=4000
	LG_GlobalToLobbyRunJS_Req       = 4001 // 通知Lobby执行JS返回 &#64;ID=4001
	LG_GlobalGetAllActivityInfo_Req = 4002 //获取所有活动的状态信息 &#64;ID=4002
	LG_GlobalActivityDigRoom_Req    = 4003 //请求挖宝活动房间号 &#64;ID=4003
	LG_CDKeyUse_Req                 = 4004 //请求兑换码兑换 &#64;ID=4004

	//====================LRProtocol 消息ID ====================
	LR_Test = 5000 //Logic服返回返回客户端登录结果 &#64;ID=5000

	//====================MongoProtocol 消息ID ====================

	//====================RLProtocol 消息ID ====================
	RL_Test_REQ = 6000 //客户端登录Logic服请求 &#64;ID=6000

	//====================SSProtocol 消息ID ====================

)

var (
	msgNameMap = map[uint32]string{

		CL_LOGIN_REQ:                           "CL_LOGIN_REQ",
		CL_PlayerData_REQ:                      "CL_PlayerData_REQ",
		CLHeartBeat:                            "CLHeartBeat",
		CLUpdateServerTime:                     "CLUpdateServerTime",
		CLGmReq:                                "CLGmReq",
		CL_PlayerData_LoginOut:                 "CL_PlayerData_LoginOut",
		CLUseItemReq:                           "CLUseItemReq",
		CLFuntionUnLockReq:                     "CLFuntionUnLockReq",
		CLMailAllListReq:                       "CLMailAllListReq",
		CLReadMailReq:                          "CLReadMailReq",
		CLReceiveMailReq:                       "CLReceiveMailReq",
		CLReceiveAllMailReq:                    "CLReceiveAllMailReq",
		CLDelMailReq:                           "CLDelMailReq",
		CLDelAllReadMailReq:                    "CLDelAllReadMailReq",
		CLMailQuestionAwardReq:                 "CLMailQuestionAwardReq",
		CLChangeNameReq:                        "CLChangeNameReq",
		CLChangeSignReq:                        "CLChangeSignReq",
		CLChangeGenderReq:                      "CLChangeGenderReq",
		CLNewGuideStepInfoReq:                  "CLNewGuideStepInfoReq",
		CLNewGuideClientStartReq:               "CLNewGuideClientStartReq",
		CLClientTriggerGuideReq:                "CLClientTriggerGuideReq",
		CLGuideStepFinishReq:                   "CLGuideStepFinishReq",
		CLGuideGroupFinishReq:                  "CLGuideGroupFinishReq",
		CLSignInInfoRequest:                    "CLSignInInfoRequest",
		CLSignInRewardRequest:                  "CLSignInRewardRequest",
		CLSevenDaySignInRewardRequest:          "CLSevenDaySignInRewardRequest",
		CLSignMonthRewardRequest:               "CLSignMonthRewardRequest",
		CLSyncSettingReq:                       "CLSyncSettingReq",
		CLAllFriendInvitationReq:               "CLAllFriendInvitationReq",
		CLFriendRasinRewardReq:                 "CLFriendRasinRewardReq",
		CLFriendGrowthQuestRewardReq:           "CLFriendGrowthQuestRewardReq",
		CLFriendGrowthQuestReceiveRewardReq:    "CLFriendGrowthQuestReceiveRewardReq",
		CLFriendGrowthQuestReceiveAllRewardReq: "CLFriendGrowthQuestReceiveAllRewardReq",
		CLSharePosterReq:                       "CLSharePosterReq",
		CLEnterInvitationCodeReq:               "CLEnterInvitationCodeReq",
		CLFinishCommonExpBoxData:               "CLFinishCommonExpBoxData",
		CLQuestReq:                             "CLQuestReq",
		CLQuestAwardToMailReq:                  "CLQuestAwardToMailReq",
		CLPlayerOtherInfo:                      "CLPlayerOtherInfo",
		CLGuildCreate:                          "CLGuildCreate",
		CLGuildMain:                            "CLGuildMain",
		CLGuildFastJoin:                        "CLGuildFastJoin",
		CLGuildSearch:                          "CLGuildSearch",
		CLGuildApply:                           "CLGuildApply",
		CLGuildHall:                            "CLGuildHall",
		CLGuildTech:                            "CLGuildTech",
		CLGuildTechLevelup:                     "CLGuildTechLevelup",
		CLGuildTechReset:                       "CLGuildTechReset",
		CLGuildShop:                            "CLGuildShop",
		CLGuildShopBuy:                         "CLGuildShopBuy",
		CLGuildEdit:                            "CLGuildEdit",
		CLGuildApplyMgrList:                    "CLGuildApplyMgrList",
		CLGuildMemberMgrList:                   "CLGuildMemberMgrList",
		CLGuildApplyMgr:                        "CLGuildApplyMgr",
		CLGuildMemberMgr:                       "CLGuildMemberMgr",
		CLGuildQuit:                            "CLGuildQuit",
		CLGuildDismiss:                         "CLGuildDismiss",
		CLGuildImpeach:                         "CLGuildImpeach",
		CLGuildSendWorldInvite:                 "CLGuildSendWorldInvite",
		CLGuildSendPlayerInvite:                "CLGuildSendPlayerInvite",
		CLGuildInviteJoin:                      "CLGuildInviteJoin",
		CLGuildDonate:                          "CLGuildDonate",
		CLGuildRank:                            "CLGuildRank",
		CLGuildLog:                             "CLGuildLog",
		CLGuildBargainingInfo:                  "CLGuildBargainingInfo",
		CLGuildBargainingNotice:                "CLGuildBargainingNotice",
		CLGuildBargaining:                      "CLGuildBargaining",
		CLGuildBargainingBuy:                   "CLGuildBargainingBuy",
		CLGuildBossEnter:                       "CLGuildBossEnter",
		CLGuildBossAtkBegin:                    "CLGuildBossAtkBegin",
		CLGuildBossSweep:                       "CLGuildBossSweep",
		CLGuildBossBuyCount:                    "CLGuildBossBuyCount",
		CLGuildBossRank:                        "CLGuildBossRank",
		CLSendChatInfo:                         "CLSendChatInfo",
		CLPrivateRed:                           "CLPrivateRed",
		CLWorldMsgGet:                          "CLWorldMsgGet",
		CLRecommendFriendReq:                   "CLRecommendFriendReq",
		CLSyncAllFriendsReq:                    "CLSyncAllFriendsReq",
		CLAllGiftReq:                           "CLAllGiftReq",
		CLAddOneFriendReq:                      "CLAddOneFriendReq",
		CLAgreeFriendReq:                       "CLAgreeFriendReq",
		CLRefuseFriendReq:                      "CLRefuseFriendReq",
		CLDelOneFriendReq:                      "CLDelOneFriendReq",
		CLblacklistOperationReq:                "CLblacklistOperationReq",
		CLQuestRed:                             "CLQuestRed",
		CLBILog:                                "CLBILog",
		CLClientAdReceive:                      "CLClientAdReceive",
		CLGuildTopList:                         "CLGuildTopList",
		CLHeadIconReq:                          "CLHeadIconReq",
		CLReplaceHeadIconReq:                   "CLReplaceHeadIconReq",
		CLUnlockHeadIcon:                       "CLUnlockHeadIcon",
		CLHeadFrame:                            "CLHeadFrame",
		CLReplaceHeadFrame:                     "CLReplaceHeadFrame",
		CLUnlockHeadFrame:                      "CLUnlockHeadFrame",
		CLGiftBuy:                              "CLGiftBuy",
		CLTimeGiftBuy:                          "CLTimeGiftBuy",
		CLSevenSignInGetData:                   "CLSevenSignInGetData",
		CLSevenSignInGetAward:                  "CLSevenSignInGetAward",
		CLDailySignInGetData:                   "CLDailySignInGetData",
		CLDailySignInGetAward:                  "CLDailySignInGetAward",
		CLDailySignInGetAccruedAward:           "CLDailySignInGetAccruedAward",
		CLFirstChargeGetReward:                 "CLFirstChargeGetReward",
		CLBuyFirstChargeGift:                   "CLBuyFirstChargeGift",
		CLTopupRebateGetData:                   "CLTopupRebateGetData",
		CLTopupRebateGetAward:                  "CLTopupRebateGetAward",
		CLMonthlyCardGetData:                   "CLMonthlyCardGetData",
		CLMonthlyCardBuyCard:                   "CLMonthlyCardBuyCard",
		CLMonthlyCardNewGetData:                "CLMonthlyCardNewGetData",
		CLMonthlyCardNewGetExtraReward:         "CLMonthlyCardNewGetExtraReward",
		CLGradedFundGetData:                    "CLGradedFundGetData",
		CLGradedFundBuyFund:                    "CLGradedFundBuyFund",
		CLGradedFundGetComWeal:                 "CLGradedFundGetComWeal",
		CLGradedFundGetSuperWeal:               "CLGradedFundGetSuperWeal",
		CLMissionSubmit:                        "CLMissionSubmit",
		CLMissionSubmitById:                    "CLMissionSubmitById",
		CLPaymentPreRequestReq:                 "CLPaymentPreRequestReq",
		CLGetActivityReward:                    "CLGetActivityReward",
		CLGetSevenDayActivityData:              "CLGetSevenDayActivityData",
		CLDeleteAccount:                        "CLDeleteAccount",
		CLRedeemCodeRewardReq:                  "CLRedeemCodeRewardReq",
		CLGachaBonusGetData:                    "CLGachaBonusGetData",
		CLGachaBonusGetAward:                   "CLGachaBonusGetAward",
		CLGetFuncPrevReward:                    "CLGetFuncPrevReward",
		CLGetQuestionReward:                    "CLGetQuestionReward",
		CLGachaWheelReward:                     "CLGachaWheelReward",
		CL_TowerMain:                           "CL_TowerMain",
		CL_TowerStart:                          "CL_TowerStart",
		CLTotalRechargeGetReward:               "CLTotalRechargeGetReward",
		CLInviteTaskList:                       "CLInviteTaskList",
		CLInviteTaskShare:                      "CLInviteTaskShare",
		CLInviteTaskGetReward:                  "CLInviteTaskGetReward",
		CLArenaGetData:                         "CLArenaGetData",
		CLArenaReqChallenge:                    "CLArenaReqChallenge",
		CLOpenPowerBuyingReq:                   "CLOpenPowerBuyingReq",
		CLPowerBuyingReq:                       "CLPowerBuyingReq",
		CLPowerRewardReq:                       "CLPowerRewardReq",
		CLAllPowerRewardReq:                    "CLAllPowerRewardReq",
		CLOnePowerRewardReq:                    "CLOnePowerRewardReq",
		CLHeavenlyDaoInfoReq:                   "CLHeavenlyDaoInfoReq",
		CLHeavenlyDaoPromoteReq:                "CLHeavenlyDaoPromoteReq",
		CLWeekCardReq:                          "CLWeekCardReq",
		CLOpenHookReq:                          "CLOpenHookReq",
		CLGetHookRewardReq:                     "CLGetHookRewardReq",
		CLGetHookExtraRewardReq:                "CLGetHookExtraRewardReq",
		CLTipOffReq:                            "CLTipOffReq",
		CLSendSingleGiftReq:                    "CLSendSingleGiftReq",
		CLReciveSingleGiftReq:                  "CLReciveSingleGiftReq",
		CLSendAllGiftReq:                       "CLSendAllGiftReq",
		CLReciveAllGiftReq:                     "CLReciveAllGiftReq",
		CLHeroListReq:                          "CLHeroListReq",
		CLHeroLevelUpReq:                       "CLHeroLevelUpReq",
		CLHeroAwakeLevelUpReq:                  "CLHeroAwakeLevelUpReq",
		CLLineupListReq:                        "CLLineupListReq",
		CLLineupUnlockSlot:                     "CLLineupUnlockSlot",
		CLLineupSwitchReq:                      "CLLineupSwitchReq",
		CLLineupSetReq:                         "CLLineupSetReq",
		CLLineupRenameReq:                      "CLLineupRenameReq",
		CLSeasonBuffReq:                        "CLSeasonBuffReq",
		CLMatchReq:                             "CLMatchReq",
		CLRoundBattleStartReq:                  "CLRoundBattleStartReq",
		CLSelectBufferReq:                      "CLSelectBufferReq",
		CLEnterSceneReq:                        "CLEnterSceneReq",
		CLMergeReq:                             "CLMergeReq",
		CLReadyReq:                             "CLReadyReq",
		CLRoundBattleEndReq:                    "CLRoundBattleEndReq",
		CLLeaveBattleReq:                       "CLLeaveBattleReq",
		CLClaimAdRewardReq:                     "CLClaimAdRewardReq",
		CLClaimSeasonRewardReq:                 "CLClaimSeasonRewardReq",
		CLSeasonInfoReq:                        "CLSeasonInfoReq",

		GL_Test_Res:                     "GL_Test_Res",
		Gl_GlobalMailNeedReload_Res:     "Gl_GlobalMailNeedReload_Res",
		Gl_NoticeSyncFriend_Res:         "Gl_NoticeSyncFriend_Res",
		Gl_GlobalKickPlayer_Res:         "Gl_GlobalKickPlayer_Res",
		GL_GlobalToLobbyRunJS_Res:       "GL_GlobalToLobbyRunJS_Res",
		Gl_GlobalGetAllActivityInfo_Res: "Gl_GlobalGetAllActivityInfo_Res",
		Gl_GlobalGetActivityInfo_Res:    "Gl_GlobalGetActivityInfo_Res",
		GL_CDKeyUse_Res:                 "GL_CDKeyUse_Res",
		Gl_GlobalChatBanPlayer_Res:      "Gl_GlobalChatBanPlayer_Res",
		Gl_GlobalWeather_Res:            "Gl_GlobalWeather_Res",
		GL_GlobalBroadcastEvent_Res:     "GL_GlobalBroadcastEvent_Res",
		GM2PS_SendGlobalMail:            "GM2PS_SendGlobalMail",

		LC_LOGIN_RET:                       "LC_LOGIN_RET",
		LCHeartBeat:                        "LCHeartBeat",
		LC_PlayerData_Sync:                 "LC_PlayerData_Sync",
		LC_PlayerData_Complate:             "LC_PlayerData_Complate",
		LCNotice:                           "LCNotice",
		LCLoginUpdateServerTime:            "LCLoginUpdateServerTime",
		LCUpdateServerTime:                 "LCUpdateServerTime",
		LCNotifyCrossDay:                   "LCNotifyCrossDay",
		LC_PlayerResource_Sync:             "LC_PlayerResource_Sync",
		LCDropItemListRes:                  "LCDropItemListRes",
		LCSceneDropItemListRes:             "LCSceneDropItemListRes",
		LCSyncRedDotData:                   "LCSyncRedDotData",
		LCSyncBagItemListRst:               "LCSyncBagItemListRst",
		LCAllItemList:                      "LCAllItemList",
		LCSingleItem:                       "LCSingleItem",
		LCAttributeLevelUp:                 "LCAttributeLevelUp",
		LC_AddExp_RES:                      "LC_AddExp_RES",
		LCOpenHook:                         "LCOpenHook",
		LCReceiveHook:                      "LCReceiveHook",
		LCReceiveHookSweep:                 "LCReceiveHookSweep",
		LCMailListRes:                      "LCMailListRes",
		LCReadMailRes:                      "LCReadMailRes",
		LCReceiveMailRes:                   "LCReceiveMailRes",
		LCReceiveAllMailRes:                "LCReceiveAllMailRes",
		LCDelMailRes:                       "LCDelMailRes",
		LCDelAllReadMailRes:                "LCDelAllReadMailRes",
		LCMailQuestionAwardRes:             "LCMailQuestionAwardRes",
		LCChangeNameRes:                    "LCChangeNameRes",
		LCChangeSignRst:                    "LCChangeSignRst",
		LCChangeGenderRst:                  "LCChangeGenderRst",
		LCCancellationRst:                  "LCCancellationRst",
		LCInitNewGuideStepInfoRes:          "LCInitNewGuideStepInfoRes",
		LCNewGuideStepInfoRes:              "LCNewGuideStepInfoRes",
		LCUpCurNewGuideStepInfoRes:         "LCUpCurNewGuideStepInfoRes",
		LCSyncNewGuideInfoRsp:              "LCSyncNewGuideInfoRsp",
		LCSignInInfoResponse:               "LCSignInInfoResponse",
		LCSignInGetRewardResponse:          "LCSignInGetRewardResponse",
		LCSignMonthGetRewardResponse:       "LCSignMonthGetRewardResponse",
		LCSignSevenRewardResponse:          "LCSignSevenRewardResponse",
		LCSystemShowErrorMessageBox:        "LCSystemShowErrorMessageBox",
		LCSyncSettingRes:                   "LCSyncSettingRes",
		LCSyncAllFriendInvitationRes:       "LCSyncAllFriendInvitationRes",
		LCFriendInvitationMainRes:          "LCFriendInvitationMainRes",
		LCSyncFriendInvitationRes:          "LCSyncFriendInvitationRes",
		LCSyncFriendGrowthQuestRewardRes:   "LCSyncFriendGrowthQuestRewardRes",
		LCSyncFriendGrowthReceiveRewardRes: "LCSyncFriendGrowthReceiveRewardRes",
		LCSyncSharePosterRes:               "LCSyncSharePosterRes",
		LCSyncEnterInvitationCodeRes:       "LCSyncEnterInvitationCodeRes",
		LCSyncChargeRes:                    "LCSyncChargeRes",
		LC_MIDAS_DILIVERY_SUCCESS:          "LC_MIDAS_DILIVERY_SUCCESS",
		LC_MIDAS_DILIVERY_RepeatPurchase:   "LC_MIDAS_DILIVERY_RepeatPurchase",
		LC_MIDAS_DIAMOND_NTF:               "LC_MIDAS_DIAMOND_NTF",
		LC_MIDAS_ITEM_NTF:                  "LC_MIDAS_ITEM_NTF",
		LCReConnectComplete:                "LCReConnectComplete",
		LCAFData:                           "LCAFData",
		LCCommonExpBoxData:                 "LCCommonExpBoxData",
		LCFinishCommonExpBoxData:           "LCFinishCommonExpBoxData",
		LCPushActivityInfo:                 "LCPushActivityInfo",
		LCGetActivityReward:                "LCGetActivityReward",
		LCGetSevenDayActivityData:          "LCGetSevenDayActivityData",
		LCServerTimeDailyFiveRes:           "LCServerTimeDailyFiveRes",
		LCRankListRes:                      "LCRankListRes",
		LCRankRewardRes:                    "LCRankRewardRes",
		LCClientAdData:                     "LCClientAdData",
		LCSyncQuestList:                    "LCSyncQuestList",
		LCSyncSingleQuest:                  "LCSyncSingleQuest",
		LCPlayerOtherInfo:                  "LCPlayerOtherInfo",
		LCGuildMain:                        "LCGuildMain",
		LCGuildRecommendList:               "LCGuildRecommendList",
		LCGuildSearch:                      "LCGuildSearch",
		LCGuildApply:                       "LCGuildApply",
		LCGuildHall:                        "LCGuildHall",
		LCGuildTech:                        "LCGuildTech",
		LCGuildTechLevelup:                 "LCGuildTechLevelup",
		LCGuildTechReset:                   "LCGuildTechReset",
		LCGuildEdit:                        "LCGuildEdit",
		LCGuildApplyMgrList:                "LCGuildApplyMgrList",
		LCGuildMemberMgrList:               "LCGuildMemberMgrList",
		LCGuildApplyMgr:                    "LCGuildApplyMgr",
		LCGuildMemberMgr:                   "LCGuildMemberMgr",
		LCGuildQuit:                        "LCGuildQuit",
		LCGuildDismiss:                     "LCGuildDismiss",
		LCGuildImpeach:                     "LCGuildImpeach",
		LCGuildSendWorldInvite:             "LCGuildSendWorldInvite",
		LCGuildSendPlayerInvite:            "LCGuildSendPlayerInvite",
		LCGuildInviteJoin:                  "LCGuildInviteJoin",
		LCGuildDonate:                      "LCGuildDonate",
		LCGuildRank:                        "LCGuildRank",
		LCGuildLog:                         "LCGuildLog",
		LCGuildBargainingInfo:              "LCGuildBargainingInfo",
		LCGuildBargainingNotice:            "LCGuildBargainingNotice",
		LCGuildBargaining:                  "LCGuildBargaining",
		LCGuildBargainingBuy:               "LCGuildBargainingBuy",
		LC_Guild_Sync:                      "LC_Guild_Sync",
		LCGuildBossEnter:                   "LCGuildBossEnter",
		LCGuildBossAtkBegin:                "LCGuildBossAtkBegin",
		LCGuildBossSweep:                   "LCGuildBossSweep",
		LCGuildBossBuyCount:                "LCGuildBossBuyCount",
		LCGuildBossRank:                    "LCGuildBossRank",
		LCGuildCreate:                      "LCGuildCreate",
		LCGuildShop:                        "LCGuildShop",
		LCGuildShopBuy:                     "LCGuildShopBuy",
		LCSyncChatInfo:                     "LCSyncChatInfo",
		LCBroadcastGuideChatInfo:           "LCBroadcastGuideChatInfo",
		LCBroadcastPrivateChatInfo:         "LCBroadcastPrivateChatInfo",
		LCLoginGetAllPrivateChatInfo:       "LCLoginGetAllPrivateChatInfo",
		LCLoginGetAllChatInfo:              "LCLoginGetAllChatInfo",
		LCSyncRecommendFriendRes:           "LCSyncRecommendFriendRes",
		LCSyncAllFriendsRes:                "LCSyncAllFriendsRes",
		LCBlackPlayerRes:                   "LCBlackPlayerRes",
		LCSyncRequestAddOneFriendRes:       "LCSyncRequestAddOneFriendRes",
		LCSyncAddFriendRes:                 "LCSyncAddFriendRes",
		LCSyncRefuseFriendRes:              "LCSyncRefuseFriendRes",
		LCSyncDelOneFriendRes:              "LCSyncDelOneFriendRes",
		LCblacklistOperationRes:            "LCblacklistOperationRes",
		LCSyncCreateFriendTime:             "LCSyncCreateFriendTime",
		LCSyncGiftInfoRes:                  "LCSyncGiftInfoRes",
		LCSyncRecGiftInfoRes:               "LCSyncRecGiftInfoRes",
		LCSyncSendGiftInfoRes:              "LCSyncSendGiftInfoRes",
		LCShowRecGift:                      "LCShowRecGift",
		LCSyncSendApplyFriend:              "LCSyncSendApplyFriend",
		LCApplyFriendRes:                   "LCApplyFriendRes",
		LCCDKeyUse:                         "LCCDKeyUse",
		LCReNetReconnect:                   "LCReNetReconnect",
		LCGuildTopList:                     "LCGuildTopList",
		LCSyncHeadIconList:                 "LCSyncHeadIconList",
		LCSyncSingleHeadIcon:               "LCSyncSingleHeadIcon",
		LCReplaceHeadIcon:                  "LCReplaceHeadIcon",
		LCUnlockHeadIcon:                   "LCUnlockHeadIcon",
		LCSyncHeadFrameList:                "LCSyncHeadFrameList",
		LCSyncSingleHeadFrame:              "LCSyncSingleHeadFrame",
		LCReplaceHeadFrame:                 "LCReplaceHeadFrame",
		LCUnlockHeadFrame:                  "LCUnlockHeadFrame",
		LCGiftBuyRes:                       "LCGiftBuyRes",
		LCShopGiftList:                     "LCShopGiftList",
		LCTimeGiftBuyRes:                   "LCTimeGiftBuyRes",
		LCTimeShopGiftList:                 "LCTimeShopGiftList",
		LCSevenSignInGetDataRe:             "LCSevenSignInGetDataRe",
		LCSevenSignInGetAwardRe:            "LCSevenSignInGetAwardRe",
		LCDailySignInGetDataRe:             "LCDailySignInGetDataRe",
		LCDailySignInGetAwardRe:            "LCDailySignInGetAwardRe",
		LCDailySignInGetAccruedAwardRe:     "LCDailySignInGetAccruedAwardRe",
		LCGetFristChargeRewardRe:           "LCGetFristChargeRewardRe",
		LCFirstChargeGetDataRe:             "LCFirstChargeGetDataRe",
		LCBuyFirstChargeGiftRet:            "LCBuyFirstChargeGiftRet",
		LCTopupRebateGetDataRe:             "LCTopupRebateGetDataRe",
		LCTopupRebateGetAwardRe:            "LCTopupRebateGetAwardRe",
		LCMonthlyCardGetDataRe:             "LCMonthlyCardGetDataRe",
		LCMonthlyCardBuyCardRe:             "LCMonthlyCardBuyCardRe",
		LCMonthlyCardNewGetDataRe:          "LCMonthlyCardNewGetDataRe",
		LCMonthlyCardNewGetExtraReward:     "LCMonthlyCardNewGetExtraReward",
		LCGradedFundGetDataRe:              "LCGradedFundGetDataRe",
		LCGradedFundBuyFundRe:              "LCGradedFundBuyFundRe",
		LCGradedFundGetComWealRe:           "LCGradedFundGetComWealRe",
		LCGradedFundGetSuperWealRe:         "LCGradedFundGetSuperWealRe",
		LCMissionGetData:                   "LCMissionGetData",
		LCMissionSubmit:                    "LCMissionSubmit",
		LCCommonAwardDisplay:               "LCCommonAwardDisplay",
		LCPaymentPreRequestRe:              "LCPaymentPreRequestRe",
		LCPaymentRequestTestRe:             "LCPaymentRequestTestRe",
		LCFunctionOpenPushAll:              "LCFunctionOpenPushAll",
		LCFunctionOpenSync:                 "LCFunctionOpenSync",
		LCDeleteAccount:                    "LCDeleteAccount",
		LCRedeemCodeRewardRst:              "LCRedeemCodeRewardRst",
		LCGachaBonusGetDataRe:              "LCGachaBonusGetDataRe",
		LCGachaBonusGetAwardRe:             "LCGachaBonusGetAwardRe",
		LCFuncPrevPushAll:                  "LCFuncPrevPushAll",
		LCFuncPrevReward:                   "LCFuncPrevReward",
		LCGetQuestionReward:                "LCGetQuestionReward",
		LCSyncRewardQuestion:               "LCSyncRewardQuestion",
		LCGachaWheelReward:                 "LCGachaWheelReward",
		LC_TowerMain:                       "LC_TowerMain",
		LC_SyncTower:                       "LC_SyncTower",
		LCTotalRechargeGetReward:           "LCTotalRechargeGetReward",
		LCTotalRechargeData:                "LCTotalRechargeData",
		LCInviteTaskList:                   "LCInviteTaskList",
		LCInviteTaskShare:                  "LCInviteTaskShare",
		LCInviteTaskGetReward:              "LCInviteTaskGetReward",
		LCArenaGetData:                     "LCArenaGetData",
		LCArenaChallengeResult:             "LCArenaChallengeResult",
		LCOpenPowerBuyingRes:               "LCOpenPowerBuyingRes",
		LCPowerBuyingRes:                   "LCPowerBuyingRes",
		LCSyncPowerRewardList:              "LCSyncPowerRewardList",
		LCAllPowerRewardRes:                "LCAllPowerRewardRes",
		LCOnePowerRewardRes:                "LCOnePowerRewardRes",
		LCHeavenlyDaoInfoRes:               "LCHeavenlyDaoInfoRes",
		LCHeavenlyDaoPromoteRes:            "LCHeavenlyDaoPromoteRes",
		LCWeekCardDataRes:                  "LCWeekCardDataRes",
		LCTipOffRes:                        "LCTipOffRes",
		LCHeroListResp:                     "LCHeroListResp",
		LCHeroLevelUpRes:                   "LCHeroLevelUpRes",
		LCHeroAwakeLevelUpRes:              "LCHeroAwakeLevelUpRes",
		LCLineupListResp:                   "LCLineupListResp",
		LCLineupUnlockSlotRes:              "LCLineupUnlockSlotRes",
		LCLineupSwitchResp:                 "LCLineupSwitchResp",
		LCLineupSetReq:                     "LCLineupSetReq",
		LCLineupRenameResp:                 "LCLineupRenameResp",
		LCSeasonBuffRes:                    "LCSeasonBuffRes",
		LCMatchRsp:                         "LCMatchRsp",
		LCMatchSuccessNotify:               "LCMatchSuccessNotify",
		LCRoundBattleStartResp:             "LCRoundBattleStartResp",
		LCRoundStartNotify:                 "LCRoundStartNotify",
		LCSelectBufferResp:                 "LCSelectBufferResp",
		LCMergeRsp:                         "LCMergeRsp",
		LCReadyRsp:                         "LCReadyRsp",
		LCRoundBattleEndResp:               "LCRoundBattleEndResp",
		LCRoundBattleStartNotify:           "LCRoundBattleStartNotify",
		LCRoundBattleEndNotify:             "LCRoundBattleEndNotify",
		LCBattleEndNotify:                  "LCBattleEndNotify",
		LCLeaveBattleRsp:                   "LCLeaveBattleRsp",
		LCClaimAdRewardRsp:                 "LCClaimAdRewardRsp",
		LCClaimSeasonRewardRsp:             "LCClaimSeasonRewardRsp",
		LCSeasonInfoRsp:                    "LCSeasonInfoRsp",
		LCSeasonResetNotify:                "LCSeasonResetNotify",

		LG_Test_Req:                     "LG_Test_Req",
		LG_GlobalToLobbyRunJS_Req:       "LG_GlobalToLobbyRunJS_Req",
		LG_GlobalGetAllActivityInfo_Req: "LG_GlobalGetAllActivityInfo_Req",
		LG_GlobalActivityDigRoom_Req:    "LG_GlobalActivityDigRoom_Req",
		LG_CDKeyUse_Req:                 "LG_CDKeyUse_Req",

		LR_Test: "LR_Test",

		RL_Test_REQ: "RL_Test_REQ",
	}
)

func MsgName(msgId uint32) string {
	if name, ok := msgNameMap[msgId]; ok {
		return name
	}
	return "unknown msg_id:" + strconv.Itoa(int(msgId))
}
