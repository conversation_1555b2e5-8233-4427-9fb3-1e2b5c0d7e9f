package nats_service

import (
	"context"
	"liteframe/internal/common/natsrpc"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/table"
	"liteframe/internal/common/table/table_data"
	"liteframe/internal/game-logic/matchserver/global"
	"liteframe/pkg/listmap"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
	"math/rand"
	"time"
)

type NormalMatch struct {
	// 简单匹配
	listMap  *listmap.ListMap
	addCh    chan *PlayerInfo
	cancelCh chan uint64
	closeCh  chan struct{}
}

func newNormalMatch() *NormalMatch {
	return &NormalMatch{
		listMap:  listmap.NewListMap(),
		addCh:    make(chan *PlayerInfo, channelSize),
		cancelCh: make(chan uint64, channelSize),
		closeCh:  make(chan struct{}),
	}
}

func (m *NormalMatch) RequestMatch(player *public.PBBattlePlayerInfo, team *public.PBBattleTeamInfo) {
	m.addCh <- &PlayerInfo{
		battleInfo: player,
		teamInfo:   team,
		beginTime:  time.Now().Unix(),
	}
}

func (m *NormalMatch) Start() {
	util.Go(m.Run)

	//m.requestCreateRoom(nil, nil)
}

func (m *NormalMatch) match() {
	if m.listMap.Len() < 2 {
		return
	}

	tierMap := make(map[int32][]*PlayerInfo)
	// 每个tick 最多匹配500个  分段位 排序
	for i := 0; i < maxMatchPerTick; i++ {
		first := m.listMap.PopFront()
		if first == nil {
			break
		}
		p1 := first.V.(*PlayerInfo)
		if tierMap[p1.battleInfo.Kills] == nil {
			tierMap[p1.battleInfo.Kills] = []*PlayerInfo{}
		}
		tierMap[p1.battleInfo.Kills] = append(tierMap[p1.battleInfo.Kills], p1)
	}

	//var ex *PlayerInfo
	//// 按分段位排序
	//// 按照段位从到小遍历，
	//// 每个段位内再按照积分排序
	//// 相差三个段位及以上不匹配 放回下次重匹配
	//maxTier := int32(table.GetTable().ArenaDuan.Count())
	//
	//for tier := maxTier; tier >= 1; tier-- {
	//	players, ok := tierMap[tier]
	//	if !ok || len(players) == 0 {
	//		continue
	//	}
	//	f := func(i, j int) bool {
	//		if players[i].ArenaScore != players[j].ArenaScore {
	//			return players[i].ArenaScore > players[j].ArenaScore
	//		} else {
	//			return players[i].Level > players[j].Level
	//		}
	//	}
	//	sort.Slice(players, f)
	//	for i := 0; i < len(players); i++ { // 遍历本段位
	//		if ex != nil { //上个段位有剩余玩家
	//			if math.Abs(float64(ex.ArenaTier-players[i].ArenaTier)) < ArenaMatchTierInterval {
	//				m.requestCreateRoom(players[i], ex)
	//				ex = nil
	//				continue
	//			} else {
	//				m.listMap.PushBack(ex.Uid, ex) //放回去
	//				ex = nil
	//			}
	//		}
	//		if len(players) > i+1 { //本段位剩余两个及以上
	//			//	i++
	//			m.requestCreateRoom(players[i], players[i+1])
	//			i++
	//		} else { //本段位剩余最后一个
	//			ex = players[i]
	//		}
	//	}
	//}
	//if ex != nil {
	//	m.listMap.PushBack(ex.Uid, ex) //放回去
	//}
}

func (m *NormalMatch) Run() {
	ticker := time.NewTicker(time.Millisecond * time.Duration(100))
	for {
		select {
		case t := <-ticker.C:
			m.match()
			m.evictTimeout(t)
		case player := <-m.addCh:
			m.listMap.PushBack(player.battleInfo.Uid, player)
		case uid := <-m.cancelCh:
			m.listMap.RemoveByKey(uid)
		case <-m.closeCh:
			log.Info("match manager close")
			return
		}
	}
}

func (m *NormalMatch) Stop() {
	close(m.closeCh)
}

func (m *NormalMatch) Match(req *natsrpc.MatchRequest) {
	m.RequestMatch(req.Player, req.Team)
}

func (m *NormalMatch) CancelMatch(req *natsrpc.CancelMatchRequest) {
	m.cancelCh <- req.Uid
}

func (m *NormalMatch) evictTimeout(t time.Time) {
	for e := m.listMap.Front(); e != nil; {
		pair := e.Value.(*listmap.Pair)
		info := pair.V.(*PlayerInfo)

		e = e.Next()

		// 根据匹配类型确定超时时间
		timeoutSeconds := m.getMatchTimeout(info.battleInfo.Throphy)
		if t.Unix()-info.beginTime < int64(timeoutSeconds) {
			return
		}
		m.listMap.PopFront()

		// 根据匹配类型处理超时
		matchType := m.getMatchType(info.battleInfo.Throphy)
		m.handleMatchTimeout(info, matchType)
	}
}

func (m *NormalMatch) testCopyPlayer(p *PlayerInfo) *PlayerInfo {
	copyInfo := *p

	// 需要深拷贝 battleInfo，否则会修改原始对象
	if p.battleInfo != nil {
		battleInfoCopy := *p.battleInfo
		copyInfo.battleInfo = &battleInfoCopy

		// 生成随机UID，避免与原始玩家或其他机器人重复
		randOffset := 10000 + rand.Intn(90000)
		copyInfo.battleInfo.Uid = 90000000000 + uint64(randOffset)

		// 修改名称以表明这是AI玩家
		copyInfo.battleInfo.Name = "AI_" + copyInfo.battleInfo.Name
	}

	return &copyInfo
}

func (m *NormalMatch) requestCreateRoom(p1, p2, p3, p4 *PlayerInfo) {

	//user := &public.PBBattlePlayerInfo{
	//	Uid:   100,
	//	Level: 10,
	//	Name:  "player1",
	//}
	req := natsrpc.CreateBattleReq{
		CreateInfo: &natsrpc.PBCreateBattleInfo{},
	}
	req.CreateInfo.Players = append(req.CreateInfo.Players, p1.battleInfo)
	req.CreateInfo.Teams = append(req.CreateInfo.Teams, p1.teamInfo)
	req.CreateInfo.Players = append(req.CreateInfo.Players, p2.battleInfo)
	req.CreateInfo.Teams = append(req.CreateInfo.Teams, p2.teamInfo)
	req.CreateInfo.Players = append(req.CreateInfo.Players, p3.battleInfo)
	req.CreateInfo.Teams = append(req.CreateInfo.Teams, p3.teamInfo)
	req.CreateInfo.Players = append(req.CreateInfo.Players, p4.battleInfo)
	req.CreateInfo.Teams = append(req.CreateInfo.Teams, p4.teamInfo)

	global.BattleRpcClient.AsyncCreateBattle(context.Background(), &req, func(response *natsrpc.CreateBattleResp, err error) {
		m.notifyMatchResult(p1, p2, p3, p4, response.BattleId, response.ServerId, 0)
	})
}

func (m *NormalMatch) notifyMatchResult(self, p1, p2, p3 *PlayerInfo, battleId int64, serverId int64, createTime int64) {

	targets := make([]*public.PBBattlePlayerInfo, 0)
	targets = append(targets, self.battleInfo) // 包含自己
	targets = append(targets, p1.battleInfo)
	targets = append(targets, p2.battleInfo)
	targets = append(targets, p3.battleInfo)
	if err := global.GameRpcClient.MatchResult(context.Background(), &natsrpc.MatchResultRequest{
		Success:    true,
		Uid:        self.battleInfo.Uid,
		BattleId:   battleId,
		ServerId:   serverId,
		Target:     targets,
		CreateTime: 0,
	}, self.battleInfo.ServerId); err != nil {
		log.Error("notify match result error", log.Err(err), log.Kv("uid", self.battleInfo.Uid))
	}
}

// 获取匹配类型
func (m *NormalMatch) getMatchType(trophy int32) int32 {
	// 测试匹配类型0：用于测试，10秒超时填充机器人
	if trophy == 0 {
		return 0
	}

	// 从MainRank表获取匹配类型
	mainRankTable := table.GetTable().TableMainRank
	if mainRankTable == nil {
		log.Error("MainRank table not found! This is a configuration error!")
		// 不返回默认值，让问题暴露出来
		return -1 // 返回无效值，让后续逻辑能检测到错误
	}

	// 遍历表找到对应的段位配置
	var matchType int32 = -1 // 初始化为无效值
	var found bool = false
	mainRankTable.Foreach(func(rank *table_data.TableMainRank) bool {
		if trophy >= rank.ScoreRank {
			matchType = rank.MatchType
			found = true
		}
		return false // 继续遍历
	})

	if !found {
		log.Error("No matching rank found for trophy", log.Kv("trophy", trophy))
		return -1 // 返回无效值
	}

	log.Debug("Player match type determined",
		log.Kv("trophy", trophy),
		log.Kv("matchType", matchType))

	return matchType
}

// 获取匹配超时时间
func (m *NormalMatch) getMatchTimeout(trophy int32) int32 {
	// 暂时所有类型都使用10秒超时
	return 10
}

// 处理匹配超时
func (m *NormalMatch) handleMatchTimeout(info *PlayerInfo, matchType int32) {
	switch matchType {
	case 0: // 测试类型：10秒超时，填充3个机器人
		m.createTestBattle(info)
	case 1: // PVE类型：填充3个机器人
		m.createPVEBattle(info)
	case 2: // 异步PVP类型：暂时用机器人代替
		m.createAsyncPVPBattle(info)
	case 3: // 实时PVP类型：填充机器人
		m.createRealtimePVPBattle(info)
	case 4: // 复合类型：降级为异步PVP
		m.createAsyncPVPBattle(info)
	case -1: // 无效匹配类型，配置错误
		log.Error("Invalid match type, cannot create battle",
			log.Kv("uid", info.battleInfo.Uid),
			log.Kv("trophy", info.battleInfo.Throphy),
			log.Kv("matchType", matchType))
		return
	default:
		log.Error("Unknown match type",
			log.Kv("uid", info.battleInfo.Uid),
			log.Kv("matchType", matchType))
		return
	}
}

// 创建测试战斗
func (m *NormalMatch) createTestBattle(info *PlayerInfo) {
	robot2 := m.testCopyPlayer(info)
	robot3 := m.testCopyPlayer(info)
	robot4 := m.testCopyPlayer(info)
	if robot2 != nil {
		m.requestCreateRoom(info, robot2, robot3, robot4)
		log.Info("test match timeout", log.Kv("uid", info.battleInfo.Uid))
	}
}

// 创建PVE战斗
func (m *NormalMatch) createPVEBattle(info *PlayerInfo) {
	// PVE类型：暂时保持和测试模式一致，填充3个copy的机器人
	robot2 := m.testCopyPlayer(info)
	robot3 := m.testCopyPlayer(info)
	robot4 := m.testCopyPlayer(info)
	if robot2 != nil {
		m.requestCreateRoom(info, robot2, robot3, robot4)
		log.Info("PVE match timeout", log.Kv("uid", info.battleInfo.Uid))
	}
}

// 创建异步PVP战斗
func (m *NormalMatch) createAsyncPVPBattle(info *PlayerInfo) {
	// 异步PVP类型：暂时用机器人代替，后续可以从数据库获取真实玩家数据
	robot2 := m.testCopyPlayer(info)
	robot3 := m.testCopyPlayer(info)
	robot4 := m.testCopyPlayer(info)
	if robot2 != nil {
		m.requestCreateRoom(info, robot2, robot3, robot4)
		log.Info("async PVP match timeout", log.Kv("uid", info.battleInfo.Uid))
	}
}

// 创建实时PVP战斗
func (m *NormalMatch) createRealtimePVPBattle(info *PlayerInfo) {
	// 实时PVP类型：填充机器人
	robot2 := m.testCopyPlayer(info)
	robot3 := m.testCopyPlayer(info)
	robot4 := m.testCopyPlayer(info)
	if robot2 != nil {
		m.requestCreateRoom(info, robot2, robot3, robot4)
		log.Info("realtime PVP match timeout", log.Kv("uid", info.battleInfo.Uid))
	}
}

// 获取匹配分数范围 (用于后续扩展)
func (m *NormalMatch) getMatchingArea(trophy int32) [][]int32 {
	// 从MainRank表获取Area配置
	mainRankTable := table.GetTable().TableMainRank
	if mainRankTable == nil {
		log.Error("MainRank table not found! This is a configuration error!")
		return nil
	}

	// 遍历表找到对应的段位配置
	var area [][]int32
	var found bool = false
	mainRankTable.Foreach(func(rank *table_data.TableMainRank) bool {
		if trophy >= rank.ScoreRank {
			area = rank.Area
			found = true
		}
		return false // 继续遍历
	})

	if !found {
		log.Error("No matching rank found for trophy", log.Kv("trophy", trophy))
		return nil
	}

	if area == nil || len(area) == 0 {
		log.Error("Area configuration is empty for trophy", log.Kv("trophy", trophy))
		return nil
	}

	log.Debug("Player matching area determined",
		log.Kv("trophy", trophy),
		log.Kv("area", area))

	return area
}
