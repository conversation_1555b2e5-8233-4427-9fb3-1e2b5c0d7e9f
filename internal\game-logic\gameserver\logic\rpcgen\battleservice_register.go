// Code generated by rpcparse. DO NOT EDIT.

package rpcgen

import (
	"context"
	"google.golang.org/protobuf/proto"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/game-logic/gameserver/logic/player"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
)

// RegisterBattleServicecomment
func RegisterBattleService(dispatch *actor.Dispatcher, service BattleServiceInterface) {

	// userbattlematch
	MatchBattleHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLMatchReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("BattleService:MatchBattle unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCMatchRsp{}
			p.SendToClient(rpc_def.LCMatchRsp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCMatchRsp{}
			p.SendToClient(rpc_def.LCMatchRsp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.MatchBattle(ctx, p, in)
		p.SendToClient(rpc_def.LCMatchRsp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLMatchReq), MatchBattleHandler)

	// userroundbattlestart
	RoundBattleStartHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLRoundBattleStartReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("BattleService:RoundBattleStart unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCRoundBattleStartResp{}
			p.SendToClient(rpc_def.LCRoundBattleStartResp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCRoundBattleStartResp{}
			p.SendToClient(rpc_def.LCRoundBattleStartResp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.RoundBattleStart(ctx, p, in)
		p.SendToClient(rpc_def.LCRoundBattleStartResp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLRoundBattleStartReq), RoundBattleStartHandler)

	// userreadybattle
	BattleReadyHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLReadyReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("BattleService:BattleReady unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCReadyRsp{}
			p.SendToClient(rpc_def.LCReadyRsp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCReadyRsp{}
			p.SendToClient(rpc_def.LCReadyRsp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.BattleReady(ctx, p, in)
		p.SendToClient(rpc_def.LCReadyRsp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLReadyReq), BattleReadyHandler)

	// userendbattle
	RoundBattleEndHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLRoundBattleEndReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("BattleService:RoundBattleEnd unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCRoundBattleEndResp{}
			p.SendToClient(rpc_def.LCRoundBattleEndResp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCRoundBattleEndResp{}
			p.SendToClient(rpc_def.LCRoundBattleEndResp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.RoundBattleEnd(ctx, p, in)
		p.SendToClient(rpc_def.LCRoundBattleEndResp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLRoundBattleEndReq), RoundBattleEndHandler)

	// userselectbuffer
	SelectBufferHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLSelectBufferReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("BattleService:SelectBuffer unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCSelectBufferResp{}
			p.SendToClient(rpc_def.LCSelectBufferResp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCSelectBufferResp{}
			p.SendToClient(rpc_def.LCSelectBufferResp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.SelectBuffer(ctx, p, in)
		p.SendToClient(rpc_def.LCSelectBufferResp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLSelectBufferReq), SelectBufferHandler)

	// usermergehero
	MergeHeroHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLMergeReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("BattleService:MergeHero unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCMergeRsp{}
			p.SendToClient(rpc_def.LCMergeRsp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCMergeRsp{}
			p.SendToClient(rpc_def.LCMergeRsp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.MergeHero(ctx, p, in)
		p.SendToClient(rpc_def.LCMergeRsp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLMergeReq), MergeHeroHandler)

	// claimadreward
	ClaimAdRewardHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLClaimAdRewardReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("BattleService:ClaimAdReward unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCClaimAdRewardRsp{}
			p.SendToClient(rpc_def.LCClaimAdRewardRsp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCClaimAdRewardRsp{}
			p.SendToClient(rpc_def.LCClaimAdRewardRsp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.ClaimAdReward(ctx, p, in)
		p.SendToClient(rpc_def.LCClaimAdRewardRsp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLClaimAdRewardReq), ClaimAdRewardHandler)

	// leavebattle
	LeaveBattleHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLLeaveBattleReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("BattleService:LeaveBattle unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCLeaveBattleRsp{}
			p.SendToClient(rpc_def.LCLeaveBattleRsp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCLeaveBattleRsp{}
			p.SendToClient(rpc_def.LCLeaveBattleRsp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.LeaveBattle(ctx, p, in)
		p.SendToClient(rpc_def.LCLeaveBattleRsp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLLeaveBattleReq), LeaveBattleHandler)
}
