// Code generated by rpcparse. DO NOT EDIT.

// package rpc_def
package rpc_def

import (
	"google.golang.org/protobuf/proto"
	"liteframe/internal/common/protos/cs"
)

// rpc name map
var (
	RPCNameMap = map[string]uint32{
		"RequestGetActivityReward":            CLGetActivityReward,
		"RequestGetSevenDayActivityData":      CLGetSevenDayActivityData,
		"RequestArenaGetData":                 CLArenaGetData,
		"RequestArenaReqChallenge":            CLArenaReqChallenge,
		"MatchBattle":                         CLMatchReq,
		"RoundBattleStart":                    CLRoundBattleStartReq,
		"BattleReady":                         CLReadyReq,
		"RoundBattleEnd":                      CLRoundBattleEndReq,
		"SelectBuffer":                        CLSelectBufferReq,
		"MergeHero":                           CLMergeReq,
		"ClaimAdReward":                       CLClaimAdRewardReq,
		"LeaveBattle":                         CLLeaveBattleReq,
		"GetCommonBoxReward":                  CLFinishCommonExpBoxData,
		"GetFirstChargeReward":                CLFirstChargeGetReward,
		"BuyFirstChargeGift":                  CLBuyFirstChargeGift,
		"SyncAllFriendList":                   CLSyncAllFriendsReq,
		"GetFuncPrevReward":                   CLGetFuncPrevReward,
		"RequestGetGachaBonusData":            CLGachaBonusGetData,
		"RequestGetGachaBonusAward":           CLGachaBonusGetAward,
		"RequestGetGradedFundData":            CLGradedFundGetData,
		"RequestBuyGradedFund":                CLGradedFundBuyFund,
		"RequestGetGradedFundComWeal":         CLGradedFundGetComWeal,
		"RequestGetGradedFundSuperWeal":       CLGradedFundGetSuperWeal,
		"CreateGuild":                         CLGuildCreate,
		"GetGuildMainInfo":                    CLGuildHall,
		"FastJoinGuild":                       CLGuildFastJoin,
		"ApplyJoinGuild":                      CLGuildApply,
		"EditGuildInfo":                       CLGuildEdit,
		"GetGuildApplyList":                   CLGuildApplyMgrList,
		"GetGuildMemberList":                  CLGuildMemberMgrList,
		"ProcessGuildApply":                   CLGuildApplyMgr,
		"ManageGuildMember":                   CLGuildMemberMgr,
		"QuitGuild":                           CLGuildQuit,
		"DismissGuild":                        CLGuildDismiss,
		"GuildDonate":                         CLGuildDonate,
		"GuildShop":                           CLGuildShop,
		"GuildShopBuy":                        CLGuildShopBuy,
		"RequestGetHeavenlyDaoInfo":           CLHeavenlyDaoInfoReq,
		"RequestHeavenlyDaoPromote":           CLHeavenlyDaoPromoteReq,
		"HeroList":                            CLHeroListReq,
		"HeroLevelUp":                         CLHeroLevelUpReq,
		"HeroAwakeLevelUp":                    CLHeroAwakeLevelUpReq,
		"OpenHookPage":                        CLOpenHookReq,
		"GetHookReward":                       CLGetHookRewardReq,
		"GetExtraHookReward":                  CLGetHookExtraRewardReq,
		"InviteTaskList":                      CLInviteTaskList,
		"InviteTaskShare":                     CLInviteTaskShare,
		"InviteTaskGetReward":                 CLInviteTaskGetReward,
		"UseItem":                             CLUseItemReq,
		"LineupList":                          CLLineupListReq,
		"LineupUnlockSlot":                    CLLineupUnlockSlot,
		"LineupSwitch":                        CLLineupSwitchReq,
		"LineupSet":                           CLLineupSetReq,
		"LineupRename":                        CLLineupRenameReq,
		"Login":                               CL_LOGIN_REQ,
		"SyncMailAllList":                     CLMailAllListReq,
		"ReadMail":                            CLReadMailReq,
		"ReceiveMail":                         CLReceiveMailReq,
		"ReceiveAllMail":                      CLReceiveAllMailReq,
		"DelMail":                             CLDelMailReq,
		"DelAllReadMail":                      CLDelAllReadMailReq,
		"MailQuestionAward":                   CLMailQuestionAwardReq,
		"RequestSubmitMission":                CLMissionSubmit,
		"RequestSubmitMissionById":            CLMissionSubmitById,
		"OpenPowerPage":                       CLOpenPowerBuyingReq,
		"BuyPower":                            CLPowerBuyingReq,
		"SyncPowerRewardList":                 CLPowerRewardReq,
		"GetAllPowerReward":                   CLAllPowerRewardReq,
		"GetOnePowerReward":                   CLOnePowerRewardReq,
		"RequestGetMonthlyCardData":           CLMonthlyCardGetData,
		"RequestBuyMonthlyCard":               CLMonthlyCardBuyCard,
		"RequestGetMonthlyCardNewData":        CLMonthlyCardNewGetData,
		"RequestGetMonthlyCardNewExtraReward": CLMonthlyCardNewGetExtraReward,
		"GuideStepFinish":                     CLGuideStepFinishReq,
		"ClientTriggerGuide":                  CLClientTriggerGuideReq,
		"PlayerSimple":                        CL_PlayerData_REQ,
		"PlayerHeartBeat":                     CLHeartBeat,
		"ChangeSign":                          CLChangeSignReq,
		"ChangeGender":                        CLChangeGenderReq,
		"GMCommand":                           CLGmReq,
		"ChangeName":                          CLChangeNameReq,
		"UpdateServerTime":                    CLUpdateServerTime,
		"PaymentPreRequest":                   CLPaymentPreRequestReq,
		"DeleteAccount":                       CLDeleteAccount,
		"PlayerOtherInfo":                     CLPlayerOtherInfo,
		"ReplaceHeadIcon":                     CLReplaceHeadIconReq,
		"ReplaceHeadFrame":                    CLReplaceHeadFrame,
		"HeadIconReq":                         CLHeadIconReq,
		"UnlockHeadIcon":                      CLUnlockHeadIcon,
		"HeadFrameReq":                        CLHeadFrame,
		"UnlockHeadFrame":                     CLUnlockHeadFrame,
		"SyncPlayerSettingData":               CLSyncSettingReq,
		"QuestionReward":                      CLGetQuestionReward,
		"UseRedeemCode":                       CLRedeemCodeRewardReq,
		"SeasonBuff":                          CLSeasonBuffReq,
		"RequestGetSevenSignInData":           CLSevenSignInGetData,
		"RequestGetSevenSignInAward":          CLSevenSignInGetAward,
		"RequestGetDailySignInData":           CLDailySignInGetData,
		"RequestGetDailySignInAward":          CLDailySignInGetAward,
		"RequestGetDailySignInAccruedAward":   CLDailySignInGetAccruedAward,
		"RequestGachaWheelReward":             CLGachaWheelReward,
		"ShopGuyGift":                         CLGiftBuy,
		"WeekCardReq":                         CLWeekCardReq,
		"TimeShopGuyGift":                     CLTimeGiftBuy,
		"TipOff":                              CLTipOffReq,
		"RequestGetTopUpRebateData":           CLTopupRebateGetData,
		"RequestGetTopUpRebateAward":          CLTopupRebateGetAward,
		"TotalRechargeGetReward":              CLTotalRechargeGetReward,
		"TowerMain":                           CL_TowerMain,
		"TowerStart":                          CL_TowerStart,
		"ClaimSeasonReward":                   CLClaimSeasonRewardReq,
		"SeasonInfo":                          CLSeasonInfoReq,
	}
)

// RPC struct
type RPC struct {
	Name           string
	Desc           string
	RequestMsgName string
	RequestMsg     proto.Message
	RespondMsg     proto.Message
}

// RPCs map
var (
	RPCs = map[uint32]RPC{
		CLGetActivityReward: {
			Name:           "RequestGetActivityReward",
			Desc:           "requestgetactivityreward",
			RequestMsgName: "CLGetActivityReward",
			RequestMsg:     &cs.CLGetActivityReward{},
			RespondMsg:     &cs.LCGetActivityReward{},
		},
		CLGetSevenDayActivityData: {
			Name:           "RequestGetSevenDayActivityData",
			Desc:           "requestgetservertaskactivity",
			RequestMsgName: "CLGetSevenDayActivityData",
			RequestMsg:     &cs.CLGetSevenDayActivityData{},
			RespondMsg:     &cs.LCGetSevenDayActivityData{},
		},
		CLArenaGetData: {
			Name:           "RequestArenaGetData",
			Desc:           "requestgetarenadata",
			RequestMsgName: "CLArenaGetData",
			RequestMsg:     &cs.CLArenaGetData{},
			RespondMsg:     &cs.LCArenaGetData{},
		},
		CLArenaReqChallenge: {
			Name:           "RequestArenaReqChallenge",
			Desc:           "requestchallengearenataskrival",
			RequestMsgName: "CLArenaReqChallenge",
			RequestMsg:     &cs.CLArenaReqChallenge{},
			RespondMsg:     &cs.LCArenaChallengeResult{},
		},
		CLMatchReq: {
			Name:           "MatchBattle",
			Desc:           "userbattlematch",
			RequestMsgName: "CLMatchReq",
			RequestMsg:     &cs.CLMatchReq{},
			RespondMsg:     &cs.LCMatchRsp{},
		},
		CLRoundBattleStartReq: {
			Name:           "RoundBattleStart",
			Desc:           "userroundbattlestart",
			RequestMsgName: "CLRoundBattleStartReq",
			RequestMsg:     &cs.CLRoundBattleStartReq{},
			RespondMsg:     &cs.LCRoundBattleStartResp{},
		},
		CLReadyReq: {
			Name:           "BattleReady",
			Desc:           "userreadybattle",
			RequestMsgName: "CLReadyReq",
			RequestMsg:     &cs.CLReadyReq{},
			RespondMsg:     &cs.LCReadyRsp{},
		},
		CLRoundBattleEndReq: {
			Name:           "RoundBattleEnd",
			Desc:           "userendbattle",
			RequestMsgName: "CLRoundBattleEndReq",
			RequestMsg:     &cs.CLRoundBattleEndReq{},
			RespondMsg:     &cs.LCRoundBattleEndResp{},
		},
		CLSelectBufferReq: {
			Name:           "SelectBuffer",
			Desc:           "userselectbuffer",
			RequestMsgName: "CLSelectBufferReq",
			RequestMsg:     &cs.CLSelectBufferReq{},
			RespondMsg:     &cs.LCSelectBufferResp{},
		},
		CLMergeReq: {
			Name:           "MergeHero",
			Desc:           "usermergehero",
			RequestMsgName: "CLMergeReq",
			RequestMsg:     &cs.CLMergeReq{},
			RespondMsg:     &cs.LCMergeRsp{},
		},
		CLClaimAdRewardReq: {
			Name:           "ClaimAdReward",
			Desc:           "claimadreward",
			RequestMsgName: "CLClaimAdRewardReq",
			RequestMsg:     &cs.CLClaimAdRewardReq{},
			RespondMsg:     &cs.LCClaimAdRewardRsp{},
		},
		CLLeaveBattleReq: {
			Name:           "LeaveBattle",
			Desc:           "leavebattle",
			RequestMsgName: "CLLeaveBattleReq",
			RequestMsg:     &cs.CLLeaveBattleReq{},
			RespondMsg:     &cs.LCLeaveBattleRsp{},
		},
		CLFinishCommonExpBoxData: {
			Name:           "GetCommonBoxReward",
			Desc:           "GetCommonBoxReward",
			RequestMsgName: "CLFinishCommonExpBoxData",
			RequestMsg:     &cs.CLFinishCommonExpBoxData{},
			RespondMsg:     &cs.LCFinishCommonExpBoxData{},
		},
		CLFirstChargeGetReward: {
			Name:           "GetFirstChargeReward",
			Desc:           "FirstChargeGift",
			RequestMsgName: "CLFirstChargeGetReward",
			RequestMsg:     &cs.CLFirstChargeGetReward{},
			RespondMsg:     &cs.LCGetFristChargeRewardRe{},
		},
		CLBuyFirstChargeGift: {
			Name:           "BuyFirstChargeGift",
			Desc:           "BuyFirstChargeGift",
			RequestMsgName: "CLBuyFirstChargeGift",
			RequestMsg:     &cs.CLBuyFirstChargeGift{},
			RespondMsg:     &cs.LCBuyFirstChargeGiftRet{},
		},
		CLSyncAllFriendsReq: {
			Name:           "SyncAllFriendList",
			Desc:           "syncfriendlist",
			RequestMsgName: "CLSyncAllFriendsReq",
			RequestMsg:     &cs.CLSyncAllFriendsReq{},
			RespondMsg:     &cs.LCSyncAllFriendsRes{},
		},
		CLGetFuncPrevReward: {
			Name:           "GetFuncPrevReward",
			Desc:           "GetFunctionpreviewreward",
			RequestMsgName: "CLGetFuncPrevReward",
			RequestMsg:     &cs.CLGetFuncPrevReward{},
			RespondMsg:     &cs.LCFuncPrevReward{},
		},
		CLGachaBonusGetData: {
			Name:           "RequestGetGachaBonusData",
			Desc:           "requestgetgachabonusdata",
			RequestMsgName: "CLGachaBonusGetData",
			RequestMsg:     &cs.CLGachaBonusGetData{},
			RespondMsg:     &cs.LCGachaBonusGetDataRe{},
		},
		CLGachaBonusGetAward: {
			Name:           "RequestGetGachaBonusAward",
			Desc:           "requestgetgachabonusaward",
			RequestMsgName: "CLGachaBonusGetAward",
			RequestMsg:     &cs.CLGachaBonusGetAward{},
			RespondMsg:     &cs.LCGachaBonusGetAwardRe{},
		},
		CLGradedFundGetData: {
			Name:           "RequestGetGradedFundData",
			Desc:           "requestgetgradedfunddata",
			RequestMsgName: "CLGradedFundGetData",
			RequestMsg:     &cs.CLGradedFundGetData{},
			RespondMsg:     &cs.LCGradedFundGetDataRe{},
		},
		CLGradedFundBuyFund: {
			Name:           "RequestBuyGradedFund",
			Desc:           "requestbuygradedfund",
			RequestMsgName: "CLGradedFundBuyFund",
			RequestMsg:     &cs.CLGradedFundBuyFund{},
			RespondMsg:     &cs.LCGradedFundBuyFundRe{},
		},
		CLGradedFundGetComWeal: {
			Name:           "RequestGetGradedFundComWeal",
			Desc:           "requestgetgradedfundcomweal",
			RequestMsgName: "CLGradedFundGetComWeal",
			RequestMsg:     &cs.CLGradedFundGetComWeal{},
			RespondMsg:     &cs.LCGradedFundGetComWealRe{},
		},
		CLGradedFundGetSuperWeal: {
			Name:           "RequestGetGradedFundSuperWeal",
			Desc:           "requestgetgradedfundsuperweal",
			RequestMsgName: "CLGradedFundGetSuperWeal",
			RequestMsg:     &cs.CLGradedFundGetSuperWeal{},
			RespondMsg:     &cs.LCGradedFundGetSuperWealRe{},
		},
		CLGuildCreate: {
			Name:           "CreateGuild",
			Desc:           "CreateGuild",
			RequestMsgName: "CLGuildCreate",
			RequestMsg:     &cs.CLGuildCreate{},
			RespondMsg:     &cs.LCGuildCreate{},
		},
		CLGuildHall: {
			Name:           "GetGuildMainInfo",
			Desc:           "GetGuildMainInfo",
			RequestMsgName: "CLGuildHall",
			RequestMsg:     &cs.CLGuildHall{},
			RespondMsg:     &cs.LCGuildHall{},
		},
		CLGuildFastJoin: {
			Name:           "FastJoinGuild",
			Desc:           "FastJoinGuild",
			RequestMsgName: "CLGuildFastJoin",
			RequestMsg:     &cs.CLGuildFastJoin{},
			RespondMsg:     &cs.LCGuildApply{},
		},
		CLGuildApply: {
			Name:           "ApplyJoinGuild",
			Desc:           "ApplyJoinGuild",
			RequestMsgName: "CLGuildApply",
			RequestMsg:     &cs.CLGuildApply{},
			RespondMsg:     &cs.LCGuildApply{},
		},
		CLGuildEdit: {
			Name:           "EditGuildInfo",
			Desc:           "EditGuildInfo",
			RequestMsgName: "CLGuildEdit",
			RequestMsg:     &cs.CLGuildEdit{},
			RespondMsg:     &cs.LCGuildEdit{},
		},
		CLGuildApplyMgrList: {
			Name:           "GetGuildApplyList",
			Desc:           "GetGuildApplyList",
			RequestMsgName: "CLGuildApplyMgrList",
			RequestMsg:     &cs.CLGuildApplyMgrList{},
			RespondMsg:     &cs.LCGuildApplyMgrList{},
		},
		CLGuildMemberMgrList: {
			Name:           "GetGuildMemberList",
			Desc:           "GetGuildMemberList",
			RequestMsgName: "CLGuildMemberMgrList",
			RequestMsg:     &cs.CLGuildMemberMgrList{},
			RespondMsg:     &cs.LCGuildMemberMgrList{},
		},
		CLGuildApplyMgr: {
			Name:           "ProcessGuildApply",
			Desc:           "ProcessGuildApply",
			RequestMsgName: "CLGuildApplyMgr",
			RequestMsg:     &cs.CLGuildApplyMgr{},
			RespondMsg:     &cs.LCGuildApplyMgr{},
		},
		CLGuildMemberMgr: {
			Name:           "ManageGuildMember",
			Desc:           "ManageGuildMember",
			RequestMsgName: "CLGuildMemberMgr",
			RequestMsg:     &cs.CLGuildMemberMgr{},
			RespondMsg:     &cs.LCGuildMemberMgr{},
		},
		CLGuildQuit: {
			Name:           "QuitGuild",
			Desc:           "QuitGuild",
			RequestMsgName: "CLGuildQuit",
			RequestMsg:     &cs.CLGuildQuit{},
			RespondMsg:     &cs.LCGuildQuit{},
		},
		CLGuildDismiss: {
			Name:           "DismissGuild",
			Desc:           "DismissGuild",
			RequestMsgName: "CLGuildDismiss",
			RequestMsg:     &cs.CLGuildDismiss{},
			RespondMsg:     &cs.LCGuildDismiss{},
		},
		CLGuildDonate: {
			Name:           "GuildDonate",
			Desc:           "GuildDonate",
			RequestMsgName: "CLGuildDonate",
			RequestMsg:     &cs.CLGuildDonate{},
			RespondMsg:     &cs.LCGuildDonate{},
		},
		CLGuildShop: {
			Name:           "GuildShop",
			Desc:           "InviteGuildMember",
			RequestMsgName: "CLGuildShop",
			RequestMsg:     &cs.CLGuildShop{},
			RespondMsg:     &cs.LCGuildShop{},
		},
		CLGuildShopBuy: {
			Name:           "GuildShopBuy",
			Desc:           "GuildShopBuy",
			RequestMsgName: "CLGuildShopBuy",
			RequestMsg:     &cs.CLGuildShopBuy{},
			RespondMsg:     &cs.LCGuildShopBuy{},
		},
		CLHeavenlyDaoInfoReq: {
			Name:           "RequestGetHeavenlyDaoInfo",
			Desc:           "requestgetheavenlydaoinfo",
			RequestMsgName: "CLHeavenlyDaoInfoReq",
			RequestMsg:     &cs.CLHeavenlyDaoInfoReq{},
			RespondMsg:     &cs.LCHeavenlyDaoInfoRes{},
		},
		CLHeavenlyDaoPromoteReq: {
			Name:           "RequestHeavenlyDaoPromote",
			Desc:           "requestpromoteheavenlydao",
			RequestMsgName: "CLHeavenlyDaoPromoteReq",
			RequestMsg:     &cs.CLHeavenlyDaoPromoteReq{},
			RespondMsg:     &cs.LCHeavenlyDaoPromoteRes{},
		},
		CLHeroListReq: {
			Name:           "HeroList",
			Desc:           "herolist",
			RequestMsgName: "CLHeroListReq",
			RequestMsg:     &cs.CLHeroListReq{},
			RespondMsg:     &cs.LCHeroListResp{},
		},
		CLHeroLevelUpReq: {
			Name:           "HeroLevelUp",
			Desc:           "herolevelup",
			RequestMsgName: "CLHeroLevelUpReq",
			RequestMsg:     &cs.CLHeroLevelUpReq{},
			RespondMsg:     &cs.LCHeroLevelUpRes{},
		},
		CLHeroAwakeLevelUpReq: {
			Name:           "HeroAwakeLevelUp",
			Desc:           "heroawakelevelup",
			RequestMsgName: "CLHeroAwakeLevelUpReq",
			RequestMsg:     &cs.CLHeroAwakeLevelUpReq{},
			RespondMsg:     &cs.LCHeroAwakeLevelUpRes{},
		},
		CLOpenHookReq: {
			Name:           "OpenHookPage",
			Desc:           "requestopenhookrewardpage",
			RequestMsgName: "CLOpenHookReq",
			RequestMsg:     &cs.CLOpenHookReq{},
			RespondMsg:     &cs.LCOpenHook{},
		},
		CLGetHookRewardReq: {
			Name:           "GetHookReward",
			Desc:           "requestgetHookReward",
			RequestMsgName: "CLGetHookRewardReq",
			RequestMsg:     &cs.CLGetHookRewardReq{},
			RespondMsg:     &cs.LCReceiveHook{},
		},
		CLGetHookExtraRewardReq: {
			Name:           "GetExtraHookReward",
			Desc:           "requestgetextraHookReward",
			RequestMsgName: "CLGetHookExtraRewardReq",
			RequestMsg:     &cs.CLGetHookExtraRewardReq{},
			RespondMsg:     &cs.LCReceiveHook{},
		},
		CLInviteTaskList: {
			Name:           "InviteTaskList",
			Desc:           "gettasklist",
			RequestMsgName: "CLInviteTaskList",
			RequestMsg:     &cs.CLInviteTaskList{},
			RespondMsg:     &cs.LCInviteTaskList{},
		},
		CLInviteTaskShare: {
			Name:           "InviteTaskShare",
			Desc:           "shareinvite",
			RequestMsgName: "CLInviteTaskShare",
			RequestMsg:     &cs.CLInviteTaskShare{},
			RespondMsg:     &cs.LCInviteTaskShare{},
		},
		CLInviteTaskGetReward: {
			Name:           "InviteTaskGetReward",
			Desc:           "gettaskreward",
			RequestMsgName: "CLInviteTaskGetReward",
			RequestMsg:     &cs.CLInviteTaskGetReward{},
			RespondMsg:     &cs.LCInviteTaskGetReward{},
		},
		CLUseItemReq: {
			Name:           "UseItem",
			Desc:           "useitem",
			RequestMsgName: "CLUseItemReq",
			RequestMsg:     &cs.CLUseItemReq{},
			RespondMsg:     &cs.LCDropItemListRes{},
		},
		CLLineupListReq: {
			Name:           "LineupList",
			Desc:           "lineuplist",
			RequestMsgName: "CLLineupListReq",
			RequestMsg:     &cs.CLLineupListReq{},
			RespondMsg:     &cs.LCLineupListResp{},
		},
		CLLineupUnlockSlot: {
			Name:           "LineupUnlockSlot",
			Desc:           "lineupunlockslot",
			RequestMsgName: "CLLineupUnlockSlot",
			RequestMsg:     &cs.CLLineupUnlockSlot{},
			RespondMsg:     &cs.LCLineupUnlockSlotRes{},
		},
		CLLineupSwitchReq: {
			Name:           "LineupSwitch",
			Desc:           "lineupswitch",
			RequestMsgName: "CLLineupSwitchReq",
			RequestMsg:     &cs.CLLineupSwitchReq{},
			RespondMsg:     &cs.LCLineupSwitchResp{},
		},
		CLLineupSetReq: {
			Name:           "LineupSet",
			Desc:           "lineupset",
			RequestMsgName: "CLLineupSetReq",
			RequestMsg:     &cs.CLLineupSetReq{},
			RespondMsg:     &cs.LCLineupSetReq{},
		},
		CLLineupRenameReq: {
			Name:           "LineupRename",
			Desc:           "lineuprename",
			RequestMsgName: "CLLineupRenameReq",
			RequestMsg:     &cs.CLLineupRenameReq{},
			RespondMsg:     &cs.LCLineupRenameResp{},
		},
		CL_LOGIN_REQ: {
			Name:           "Login",
			Desc:           "loginreq",
			RequestMsgName: "CL_LOGIN_REQ",
			RequestMsg:     &cs.CL_LOGIN_REQ{},
			RespondMsg:     &cs.LC_LOGIN_RET{},
		},
		CLMailAllListReq: {
			Name:           "SyncMailAllList",
			Desc:           "syncmailalllistreq",
			RequestMsgName: "CLMailAllListReq",
			RequestMsg:     &cs.CLMailAllListReq{},
			RespondMsg:     &cs.LCMailListRes{},
		},
		CLReadMailReq: {
			Name:           "ReadMail",
			Desc:           "handlereadmailreq",
			RequestMsgName: "CLReadMailReq",
			RequestMsg:     &cs.CLReadMailReq{},
			RespondMsg:     &cs.LCReadMailRes{},
		},
		CLReceiveMailReq: {
			Name:           "ReceiveMail",
			Desc:           "handlereceivemailreq",
			RequestMsgName: "CLReceiveMailReq",
			RequestMsg:     &cs.CLReceiveMailReq{},
			RespondMsg:     &cs.LCReceiveMailRes{},
		},
		CLReceiveAllMailReq: {
			Name:           "ReceiveAllMail",
			Desc:           "handlereceiveallmailreq",
			RequestMsgName: "CLReceiveAllMailReq",
			RequestMsg:     &cs.CLReceiveAllMailReq{},
			RespondMsg:     &cs.LCReceiveAllMailRes{},
		},
		CLDelMailReq: {
			Name:           "DelMail",
			Desc:           "handledelmailreq",
			RequestMsgName: "CLDelMailReq",
			RequestMsg:     &cs.CLDelMailReq{},
			RespondMsg:     &cs.LCDelMailRes{},
		},
		CLDelAllReadMailReq: {
			Name:           "DelAllReadMail",
			Desc:           "handledelallreadmailreq",
			RequestMsgName: "CLDelAllReadMailReq",
			RequestMsg:     &cs.CLDelAllReadMailReq{},
			RespondMsg:     &cs.LCDelAllReadMailRes{},
		},
		CLMailQuestionAwardReq: {
			Name:           "MailQuestionAward",
			Desc:           "handlemailquestionAwardreq",
			RequestMsgName: "CLMailQuestionAwardReq",
			RequestMsg:     &cs.CLMailQuestionAwardReq{},
			RespondMsg:     &cs.LCSyncQuestList{},
		},
		CLMissionSubmit: {
			Name:           "RequestSubmitMission",
			Desc:           "requestsubmitmission",
			RequestMsgName: "CLMissionSubmit",
			RequestMsg:     &cs.CLMissionSubmit{},
			RespondMsg:     &cs.LCMissionSubmit{},
		},
		CLMissionSubmitById: {
			Name:           "RequestSubmitMissionById",
			Desc:           "requestsubmitmission",
			RequestMsgName: "CLMissionSubmitById",
			RequestMsg:     &cs.CLMissionSubmitById{},
			RespondMsg:     &cs.LCMissionSubmit{},
		},
		CLOpenPowerBuyingReq: {
			Name:           "OpenPowerPage",
			Desc:           "Openbuypowerpage",
			RequestMsgName: "CLOpenPowerBuyingReq",
			RequestMsg:     &cs.CLOpenPowerBuyingReq{},
			RespondMsg:     &cs.LCOpenPowerBuyingRes{},
		},
		CLPowerBuyingReq: {
			Name:           "BuyPower",
			Desc:           "Buypower",
			RequestMsgName: "CLPowerBuyingReq",
			RequestMsg:     &cs.CLPowerBuyingReq{},
			RespondMsg:     &cs.LCPowerBuyingRes{},
		},
		CLPowerRewardReq: {
			Name:           "SyncPowerRewardList",
			Desc:           "Openpowerrewardpage",
			RequestMsgName: "CLPowerRewardReq",
			RequestMsg:     &cs.CLPowerRewardReq{},
			RespondMsg:     &cs.LCSyncPowerRewardList{},
		},
		CLAllPowerRewardReq: {
			Name:           "GetAllPowerReward",
			Desc:           "GetAllPowerReward",
			RequestMsgName: "CLAllPowerRewardReq",
			RequestMsg:     &cs.CLAllPowerRewardReq{},
			RespondMsg:     &cs.LCAllPowerRewardRes{},
		},
		CLOnePowerRewardReq: {
			Name:           "GetOnePowerReward",
			Desc:           "GetOnePowerReward",
			RequestMsgName: "CLOnePowerRewardReq",
			RequestMsg:     &cs.CLOnePowerRewardReq{},
			RespondMsg:     &cs.LCOnePowerRewardRes{},
		},
		CLMonthlyCardGetData: {
			Name:           "RequestGetMonthlyCardData",
			Desc:           "requestgetmonthlycarddata",
			RequestMsgName: "CLMonthlyCardGetData",
			RequestMsg:     &cs.CLMonthlyCardGetData{},
			RespondMsg:     &cs.LCMonthlyCardGetDataRe{},
		},
		CLMonthlyCardBuyCard: {
			Name:           "RequestBuyMonthlyCard",
			Desc:           "requestbuymonthlycard",
			RequestMsgName: "CLMonthlyCardBuyCard",
			RequestMsg:     &cs.CLMonthlyCardBuyCard{},
			RespondMsg:     &cs.LCMonthlyCardBuyCardRe{},
		},
		CLMonthlyCardNewGetData: {
			Name:           "RequestGetMonthlyCardNewData",
			Desc:           "requestgetmonthlycardnewdata",
			RequestMsgName: "CLMonthlyCardNewGetData",
			RequestMsg:     &cs.CLMonthlyCardNewGetData{},
			RespondMsg:     &cs.LCMonthlyCardNewGetDataRe{},
		},
		CLMonthlyCardNewGetExtraReward: {
			Name:           "RequestGetMonthlyCardNewExtraReward",
			Desc:           "requestgetmonthlycardnewextrareward",
			RequestMsgName: "CLMonthlyCardNewGetExtraReward",
			RequestMsg:     &cs.CLMonthlyCardNewGetExtraReward{},
			RespondMsg:     &cs.LCMonthlyCardNewGetExtraReward{},
		},
		CLGuideStepFinishReq: {
			Name:           "GuideStepFinish",
			Desc:           "GuideStepFinish",
			RequestMsgName: "CLGuideStepFinishReq",
			RequestMsg:     &cs.CLGuideStepFinishReq{},
			RespondMsg:     &cs.LCSyncNewGuideInfoRsp{},
		},
		CLClientTriggerGuideReq: {
			Name:           "ClientTriggerGuide",
			Desc:           "ClientTriggerGuide",
			RequestMsgName: "CLClientTriggerGuideReq",
			RequestMsg:     &cs.CLClientTriggerGuideReq{},
			RespondMsg:     &cs.LCSyncNewGuideInfoRsp{},
		},
		CL_PlayerData_REQ: {
			Name:           "PlayerSimple",
			Desc:           "playersimpledata",
			RequestMsgName: "CL_PlayerData_REQ",
			RequestMsg:     &cs.CL_PlayerData_REQ{},
			RespondMsg:     &cs.LC_PlayerData_Sync{},
		},
		CLHeartBeat: {
			Name:           "PlayerHeartBeat",
			Desc:           "playerheartbeat",
			RequestMsgName: "CLHeartBeat",
			RequestMsg:     &cs.CLHeartBeat{},
			RespondMsg:     &cs.LCHeartBeat{},
		},
		CLChangeSignReq: {
			Name:           "ChangeSign",
			Desc:           "playerchangesign",
			RequestMsgName: "CLChangeSignReq",
			RequestMsg:     &cs.CLChangeSignReq{},
			RespondMsg:     &cs.LCChangeSignRst{},
		},
		CLChangeGenderReq: {
			Name:           "ChangeGender",
			Desc:           "playerchangegender",
			RequestMsgName: "CLChangeGenderReq",
			RequestMsg:     &cs.CLChangeGenderReq{},
			RespondMsg:     &cs.LCChangeGenderRst{},
		},
		CLGmReq: {
			Name:           "GMCommand",
			Desc:           "playergmcommand",
			RequestMsgName: "CLGmReq",
			RequestMsg:     &cs.CLGmReq{},
			RespondMsg:     &cs.LC_PlayerData_Sync{},
		},
		CLChangeNameReq: {
			Name:           "ChangeName",
			Desc:           "playerchangename",
			RequestMsgName: "CLChangeNameReq",
			RequestMsg:     &cs.CLChangeNameReq{},
			RespondMsg:     &cs.LCChangeNameRes{},
		},
		CLUpdateServerTime: {
			Name:           "UpdateServerTime",
			Desc:           "updateservertime",
			RequestMsgName: "CLUpdateServerTime",
			RequestMsg:     &cs.CLUpdateServerTime{},
			RespondMsg:     &cs.LCUpdateServerTime{},
		},
		CLPaymentPreRequestReq: {
			Name:           "PaymentPreRequest",
			Desc:           "paymentprerequest",
			RequestMsgName: "CLPaymentPreRequestReq",
			RequestMsg:     &cs.CLPaymentPreRequestReq{},
			RespondMsg:     &cs.LCPaymentPreRequestRe{},
		},
		CLDeleteAccount: {
			Name:           "DeleteAccount",
			Desc:           "deleteaccount",
			RequestMsgName: "CLDeleteAccount",
			RequestMsg:     &cs.CLDeleteAccount{},
			RespondMsg:     &cs.LCDeleteAccount{},
		},
		CLPlayerOtherInfo: {
			Name:           "PlayerOtherInfo",
			Desc:           "playerotherinfo",
			RequestMsgName: "CLPlayerOtherInfo",
			RequestMsg:     &cs.CLPlayerOtherInfo{},
			RespondMsg:     &cs.LCPlayerOtherInfo{},
		},
		CLReplaceHeadIconReq: {
			Name:           "ReplaceHeadIcon",
			Desc:           "replaceheadicon",
			RequestMsgName: "CLReplaceHeadIconReq",
			RequestMsg:     &cs.CLReplaceHeadIconReq{},
			RespondMsg:     &cs.LCReplaceHeadIcon{},
		},
		CLReplaceHeadFrame: {
			Name:           "ReplaceHeadFrame",
			Desc:           "replaceheadframe",
			RequestMsgName: "CLReplaceHeadFrame",
			RequestMsg:     &cs.CLReplaceHeadFrame{},
			RespondMsg:     &cs.LCReplaceHeadFrame{},
		},
		CLHeadIconReq: {
			Name:           "HeadIconReq",
			Desc:           "getheadiconlist",
			RequestMsgName: "CLHeadIconReq",
			RequestMsg:     &cs.CLHeadIconReq{},
			RespondMsg:     &cs.LCSyncHeadIconList{},
		},
		CLUnlockHeadIcon: {
			Name:           "UnlockHeadIcon",
			Desc:           "unlockheadicon",
			RequestMsgName: "CLUnlockHeadIcon",
			RequestMsg:     &cs.CLUnlockHeadIcon{},
			RespondMsg:     &cs.LCUnlockHeadIcon{},
		},
		CLHeadFrame: {
			Name:           "HeadFrameReq",
			Desc:           "getheadframelist",
			RequestMsgName: "CLHeadFrame",
			RequestMsg:     &cs.CLHeadFrame{},
			RespondMsg:     &cs.LCSyncHeadFrameList{},
		},
		CLUnlockHeadFrame: {
			Name:           "UnlockHeadFrame",
			Desc:           "unlockheadframe",
			RequestMsgName: "CLUnlockHeadFrame",
			RequestMsg:     &cs.CLUnlockHeadFrame{},
			RespondMsg:     &cs.LCUnlockHeadFrame{},
		},
		CLSyncSettingReq: {
			Name:           "SyncPlayerSettingData",
			Desc:           "syncplayersettingdata",
			RequestMsgName: "CLSyncSettingReq",
			RequestMsg:     &cs.CLSyncSettingReq{},
			RespondMsg:     &cs.LCSyncSettingRes{},
		},
		CLGetQuestionReward: {
			Name:           "QuestionReward",
			Desc:           "QuestionReward",
			RequestMsgName: "CLGetQuestionReward",
			RequestMsg:     &cs.CLGetQuestionReward{},
			RespondMsg:     &cs.LCGetQuestionReward{},
		},
		CLRedeemCodeRewardReq: {
			Name:           "UseRedeemCode",
			Desc:           "GetredeemCodereward",
			RequestMsgName: "CLRedeemCodeRewardReq",
			RequestMsg:     &cs.CLRedeemCodeRewardReq{},
			RespondMsg:     &cs.LCRedeemCodeRewardRst{},
		},
		CLSeasonBuffReq: {
			Name:           "SeasonBuff",
			Desc:           "seasonbuff",
			RequestMsgName: "CLSeasonBuffReq",
			RequestMsg:     &cs.CLSeasonBuffReq{},
			RespondMsg:     &cs.LCSeasonBuffRes{},
		},
		CLSevenSignInGetData: {
			Name:           "RequestGetSevenSignInData",
			Desc:           "requestgetsevensignIndata",
			RequestMsgName: "CLSevenSignInGetData",
			RequestMsg:     &cs.CLSevenSignInGetData{},
			RespondMsg:     &cs.LCSevenSignInGetDataRe{},
		},
		CLSevenSignInGetAward: {
			Name:           "RequestGetSevenSignInAward",
			Desc:           "requestgetsevensignInAward",
			RequestMsgName: "CLSevenSignInGetAward",
			RequestMsg:     &cs.CLSevenSignInGetAward{},
			RespondMsg:     &cs.LCSevenSignInGetAwardRe{},
		},
		CLDailySignInGetData: {
			Name:           "RequestGetDailySignInData",
			Desc:           "requestgetdailysignIndata",
			RequestMsgName: "CLDailySignInGetData",
			RequestMsg:     &cs.CLDailySignInGetData{},
			RespondMsg:     &cs.LCDailySignInGetDataRe{},
		},
		CLDailySignInGetAward: {
			Name:           "RequestGetDailySignInAward",
			Desc:           "requestgetdailysignInAward",
			RequestMsgName: "CLDailySignInGetAward",
			RequestMsg:     &cs.CLDailySignInGetAward{},
			RespondMsg:     &cs.LCDailySignInGetAwardRe{},
		},
		CLDailySignInGetAccruedAward: {
			Name:           "RequestGetDailySignInAccruedAward",
			Desc:           "requestgetdailysignInaccruedAward",
			RequestMsgName: "CLDailySignInGetAccruedAward",
			RequestMsg:     &cs.CLDailySignInGetAccruedAward{},
			RespondMsg:     &cs.LCDailySignInGetAccruedAwardRe{},
		},
		CLGachaWheelReward: {
			Name:           "RequestGachaWheelReward",
			Desc:           "requestgachawheelreward",
			RequestMsgName: "CLGachaWheelReward",
			RequestMsg:     &cs.CLGachaWheelReward{},
			RespondMsg:     &cs.LCGachaWheelReward{},
		},
		CLGiftBuy: {
			Name:           "ShopGuyGift",
			Desc:           "ShopBuyGift",
			RequestMsgName: "CLGiftBuy",
			RequestMsg:     &cs.CLGiftBuy{},
			RespondMsg:     &cs.LCGiftBuyRes{},
		},
		CLWeekCardReq: {
			Name:           "WeekCardReq",
			Desc:           "WeekCardReq",
			RequestMsgName: "CLWeekCardReq",
			RequestMsg:     &cs.CLWeekCardReq{},
			RespondMsg:     &cs.LCWeekCardDataRes{},
		},
		CLTimeGiftBuy: {
			Name:           "TimeShopGuyGift",
			Desc:           "TimeShopBuyGift",
			RequestMsgName: "CLTimeGiftBuy",
			RequestMsg:     &cs.CLTimeGiftBuy{},
			RespondMsg:     &cs.LCTimeGiftBuyRes{},
		},
		CLTipOffReq: {
			Name:           "TipOff",
			Desc:           "TipOff",
			RequestMsgName: "CLTipOffReq",
			RequestMsg:     &cs.CLTipOffReq{},
			RespondMsg:     &cs.LCTipOffRes{},
		},
		CLTopupRebateGetData: {
			Name:           "RequestGetTopUpRebateData",
			Desc:           "requestgettopuprebatedata",
			RequestMsgName: "CLTopupRebateGetData",
			RequestMsg:     &cs.CLTopupRebateGetData{},
			RespondMsg:     &cs.LCTopupRebateGetDataRe{},
		},
		CLTopupRebateGetAward: {
			Name:           "RequestGetTopUpRebateAward",
			Desc:           "requestgettopuprebateAward",
			RequestMsgName: "CLTopupRebateGetAward",
			RequestMsg:     &cs.CLTopupRebateGetAward{},
			RespondMsg:     &cs.LCTopupRebateGetAwardRe{},
		},
		CLTotalRechargeGetReward: {
			Name:           "TotalRechargeGetReward",
			Desc:           "totalrechargegetreward",
			RequestMsgName: "CLTotalRechargeGetReward",
			RequestMsg:     &cs.CLTotalRechargeGetReward{},
			RespondMsg:     &cs.LCTotalRechargeData{},
		},
		CL_TowerMain: {
			Name:           "TowerMain",
			Desc:           "TowerMain",
			RequestMsgName: "CL_TowerMain",
			RequestMsg:     &cs.CL_TowerMain{},
			RespondMsg:     &cs.LC_TowerMain{},
		},
		CL_TowerStart: {
			Name:           "TowerStart",
			Desc:           "TowerStart",
			RequestMsgName: "CL_TowerStart",
			RequestMsg:     &cs.CL_TowerStart{},
			RespondMsg:     &cs.LC_SyncTower{},
		},
		CLClaimSeasonRewardReq: {
			Name:           "ClaimSeasonReward",
			Desc:           "claimseasonreward",
			RequestMsgName: "CLClaimSeasonRewardReq",
			RequestMsg:     &cs.CLClaimSeasonRewardReq{},
			RespondMsg:     &cs.LCClaimSeasonRewardRsp{},
		},
		CLSeasonInfoReq: {
			Name:           "SeasonInfo",
			Desc:           "seasoninfo",
			RequestMsgName: "CLSeasonInfoReq",
			RequestMsg:     &cs.CLSeasonInfoReq{},
			RespondMsg:     &cs.LCSeasonInfoRsp{},
		},
	}
)

// Request map
var (
	RequestMap = map[uint32]RPC{
		CLGetActivityReward:            RPCs[CLGetActivityReward],
		CLGetSevenDayActivityData:      RPCs[CLGetSevenDayActivityData],
		CLArenaGetData:                 RPCs[CLArenaGetData],
		CLArenaReqChallenge:            RPCs[CLArenaReqChallenge],
		CLMatchReq:                     RPCs[CLMatchReq],
		CLRoundBattleStartReq:          RPCs[CLRoundBattleStartReq],
		CLReadyReq:                     RPCs[CLReadyReq],
		CLRoundBattleEndReq:            RPCs[CLRoundBattleEndReq],
		CLSelectBufferReq:              RPCs[CLSelectBufferReq],
		CLMergeReq:                     RPCs[CLMergeReq],
		CLClaimAdRewardReq:             RPCs[CLClaimAdRewardReq],
		CLLeaveBattleReq:               RPCs[CLLeaveBattleReq],
		CLFinishCommonExpBoxData:       RPCs[CLFinishCommonExpBoxData],
		CLFirstChargeGetReward:         RPCs[CLFirstChargeGetReward],
		CLBuyFirstChargeGift:           RPCs[CLBuyFirstChargeGift],
		CLSyncAllFriendsReq:            RPCs[CLSyncAllFriendsReq],
		CLGetFuncPrevReward:            RPCs[CLGetFuncPrevReward],
		CLGachaBonusGetData:            RPCs[CLGachaBonusGetData],
		CLGachaBonusGetAward:           RPCs[CLGachaBonusGetAward],
		CLGradedFundGetData:            RPCs[CLGradedFundGetData],
		CLGradedFundBuyFund:            RPCs[CLGradedFundBuyFund],
		CLGradedFundGetComWeal:         RPCs[CLGradedFundGetComWeal],
		CLGradedFundGetSuperWeal:       RPCs[CLGradedFundGetSuperWeal],
		CLGuildCreate:                  RPCs[CLGuildCreate],
		CLGuildHall:                    RPCs[CLGuildHall],
		CLGuildFastJoin:                RPCs[CLGuildFastJoin],
		CLGuildApply:                   RPCs[CLGuildApply],
		CLGuildEdit:                    RPCs[CLGuildEdit],
		CLGuildApplyMgrList:            RPCs[CLGuildApplyMgrList],
		CLGuildMemberMgrList:           RPCs[CLGuildMemberMgrList],
		CLGuildApplyMgr:                RPCs[CLGuildApplyMgr],
		CLGuildMemberMgr:               RPCs[CLGuildMemberMgr],
		CLGuildQuit:                    RPCs[CLGuildQuit],
		CLGuildDismiss:                 RPCs[CLGuildDismiss],
		CLGuildDonate:                  RPCs[CLGuildDonate],
		CLGuildShop:                    RPCs[CLGuildShop],
		CLGuildShopBuy:                 RPCs[CLGuildShopBuy],
		CLHeavenlyDaoInfoReq:           RPCs[CLHeavenlyDaoInfoReq],
		CLHeavenlyDaoPromoteReq:        RPCs[CLHeavenlyDaoPromoteReq],
		CLHeroListReq:                  RPCs[CLHeroListReq],
		CLHeroLevelUpReq:               RPCs[CLHeroLevelUpReq],
		CLHeroAwakeLevelUpReq:          RPCs[CLHeroAwakeLevelUpReq],
		CLOpenHookReq:                  RPCs[CLOpenHookReq],
		CLGetHookRewardReq:             RPCs[CLGetHookRewardReq],
		CLGetHookExtraRewardReq:        RPCs[CLGetHookExtraRewardReq],
		CLInviteTaskList:               RPCs[CLInviteTaskList],
		CLInviteTaskShare:              RPCs[CLInviteTaskShare],
		CLInviteTaskGetReward:          RPCs[CLInviteTaskGetReward],
		CLUseItemReq:                   RPCs[CLUseItemReq],
		CLLineupListReq:                RPCs[CLLineupListReq],
		CLLineupUnlockSlot:             RPCs[CLLineupUnlockSlot],
		CLLineupSwitchReq:              RPCs[CLLineupSwitchReq],
		CLLineupSetReq:                 RPCs[CLLineupSetReq],
		CLLineupRenameReq:              RPCs[CLLineupRenameReq],
		CL_LOGIN_REQ:                   RPCs[CL_LOGIN_REQ],
		CLMailAllListReq:               RPCs[CLMailAllListReq],
		CLReadMailReq:                  RPCs[CLReadMailReq],
		CLReceiveMailReq:               RPCs[CLReceiveMailReq],
		CLReceiveAllMailReq:            RPCs[CLReceiveAllMailReq],
		CLDelMailReq:                   RPCs[CLDelMailReq],
		CLDelAllReadMailReq:            RPCs[CLDelAllReadMailReq],
		CLMailQuestionAwardReq:         RPCs[CLMailQuestionAwardReq],
		CLMissionSubmit:                RPCs[CLMissionSubmit],
		CLMissionSubmitById:            RPCs[CLMissionSubmitById],
		CLOpenPowerBuyingReq:           RPCs[CLOpenPowerBuyingReq],
		CLPowerBuyingReq:               RPCs[CLPowerBuyingReq],
		CLPowerRewardReq:               RPCs[CLPowerRewardReq],
		CLAllPowerRewardReq:            RPCs[CLAllPowerRewardReq],
		CLOnePowerRewardReq:            RPCs[CLOnePowerRewardReq],
		CLMonthlyCardGetData:           RPCs[CLMonthlyCardGetData],
		CLMonthlyCardBuyCard:           RPCs[CLMonthlyCardBuyCard],
		CLMonthlyCardNewGetData:        RPCs[CLMonthlyCardNewGetData],
		CLMonthlyCardNewGetExtraReward: RPCs[CLMonthlyCardNewGetExtraReward],
		CLGuideStepFinishReq:           RPCs[CLGuideStepFinishReq],
		CLClientTriggerGuideReq:        RPCs[CLClientTriggerGuideReq],
		CL_PlayerData_REQ:              RPCs[CL_PlayerData_REQ],
		CLHeartBeat:                    RPCs[CLHeartBeat],
		CLChangeSignReq:                RPCs[CLChangeSignReq],
		CLChangeGenderReq:              RPCs[CLChangeGenderReq],
		CLGmReq:                        RPCs[CLGmReq],
		CLChangeNameReq:                RPCs[CLChangeNameReq],
		CLUpdateServerTime:             RPCs[CLUpdateServerTime],
		CLPaymentPreRequestReq:         RPCs[CLPaymentPreRequestReq],
		CLDeleteAccount:                RPCs[CLDeleteAccount],
		CLPlayerOtherInfo:              RPCs[CLPlayerOtherInfo],
		CLReplaceHeadIconReq:           RPCs[CLReplaceHeadIconReq],
		CLReplaceHeadFrame:             RPCs[CLReplaceHeadFrame],
		CLHeadIconReq:                  RPCs[CLHeadIconReq],
		CLUnlockHeadIcon:               RPCs[CLUnlockHeadIcon],
		CLHeadFrame:                    RPCs[CLHeadFrame],
		CLUnlockHeadFrame:              RPCs[CLUnlockHeadFrame],
		CLSyncSettingReq:               RPCs[CLSyncSettingReq],
		CLGetQuestionReward:            RPCs[CLGetQuestionReward],
		CLRedeemCodeRewardReq:          RPCs[CLRedeemCodeRewardReq],
		CLSeasonBuffReq:                RPCs[CLSeasonBuffReq],
		CLSevenSignInGetData:           RPCs[CLSevenSignInGetData],
		CLSevenSignInGetAward:          RPCs[CLSevenSignInGetAward],
		CLDailySignInGetData:           RPCs[CLDailySignInGetData],
		CLDailySignInGetAward:          RPCs[CLDailySignInGetAward],
		CLDailySignInGetAccruedAward:   RPCs[CLDailySignInGetAccruedAward],
		CLGachaWheelReward:             RPCs[CLGachaWheelReward],
		CLGiftBuy:                      RPCs[CLGiftBuy],
		CLWeekCardReq:                  RPCs[CLWeekCardReq],
		CLTimeGiftBuy:                  RPCs[CLTimeGiftBuy],
		CLTipOffReq:                    RPCs[CLTipOffReq],
		CLTopupRebateGetData:           RPCs[CLTopupRebateGetData],
		CLTopupRebateGetAward:          RPCs[CLTopupRebateGetAward],
		CLTotalRechargeGetReward:       RPCs[CLTotalRechargeGetReward],
		CL_TowerMain:                   RPCs[CL_TowerMain],
		CL_TowerStart:                  RPCs[CL_TowerStart],
		CLClaimSeasonRewardReq:         RPCs[CLClaimSeasonRewardReq],
		CLSeasonInfoReq:                RPCs[CLSeasonInfoReq],
	}
)

// Notify map
var (
	NotifyMap = map[uint32]RPC{
		LCMatchSuccessNotify:     RPCs[LCMatchSuccessNotify],
		LCRoundStartNotify:       RPCs[LCRoundStartNotify],
		LCRoundBattleStartNotify: RPCs[LCRoundBattleStartNotify],
		LCRoundBattleEndNotify:   RPCs[LCRoundBattleEndNotify],
		LCBattleEndNotify:        RPCs[LCBattleEndNotify],
		LC_PlayerData_Complate:   RPCs[LC_PlayerData_Complate],
		LC_PlayerResource_Sync:   RPCs[LC_PlayerResource_Sync],
		LCAttributeLevelUp:       RPCs[LCAttributeLevelUp],
		LC_AddExp_RES:            RPCs[LC_AddExp_RES],
		LCSeasonResetNotify:      RPCs[LCSeasonResetNotify],
	}
)
