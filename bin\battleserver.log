[BattleService] Player 10102021302 (勤奋的.矿工) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000091138 (AI_勤奋的.矿工) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000074113 (AI_勤奋的.矿工) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000068707 (AI_勤奋的.矿工) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleStateManager_436578261991426] Initialized
[PlayerManager_436578261991426] Player 10102021302 (勤奋的.矿工) Trophy 1 Health 3
[PlayerManager_436578261991426] Player 90000091138 (AI_勤奋的.矿工) Trophy 1 Health 3
[PlayerManager_436578261991426] Player 90000074113 (AI_勤奋的.矿工) Trophy 1 Health 3
[PlayerManager_436578261991426] Player 90000068707 (AI_勤奋的.矿工) Trophy 1 Health 3
[PlayerManager_436578261991426] Initialized with 4 players
[BattleInstanceManager] Initialized for battle 436578261991426
[OpponentPairManager] Initialized for battle 436578261991426
[BuffManager_436578261991426] Initialized
[CheckerBoard_436578261991426] Cleared checkerboard
[CheckerBoard_436578261991426] Initialized
[AutoChessScene_436578261991426] Event handlers registered
[AutoChessScene_436578261991426] Battle 436578261991426 initialized with 4 players, waiting for all players to enter
[SceneManager] Added AutoChessScene 436578261991426 to thread management
[BattleService] Battle 436578261991426 created successfully with 4 players
[BattleService] Battle 436578261991426 is now waiting for all players to call EnterBattle RPC
[BattleService] Current battle state: StateNone (waiting for player entry)
[BattleService] Players expected to enter: [10102021302, 90000091138, 90000074113, 90000068707]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10102021302 is real player, others are bots =====
[BattleService] Auto-entered bot player 90000091138 immediately
[BattleService] Auto-entered bot player 90000074113 immediately
[BattleService] Auto-entered bot player 90000068707 immediately
[BattleService] Battle 436578261991426 bots auto-entered: 3/4 players ready
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021302 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021302 entered battle 436578261991426 (initial), current count: 4
[BattleService] All 4 players entered battle 436578261991426, starting battle state machine
[BattleService] Entered players: [90000091138, 90000074113, 90000068707, 10102021302]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 436578261991426
[AutoChessScene_436578261991426] StartBattleStateMachine() called for battle 436578261991426
[AutoChessScene_436578261991426] BattleStateManager is ready, starting first round...
[AutoChessScene_436578261991426] Current state before starting: StateNone
[BattleStateManager_436578261991426] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_436578261991426] Round 1 has buff selection: False
[BattleStateManager_436578261991426] Publishing RoundStartedEvent for round 1
[AutoChessScene_436578261991426] Round 1 started
[BattleStateManager_436578261991426] Setting state to StateRoundStart for round 1
[BattleStateManager_436578261991426] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_436578261991426] HandleRoundStart: 4 active players
[AutoChessScene_436578261991426] Valid player: 10102021302, Health: 3
[AutoChessScene_436578261991426] Valid player: 90000091138, Health: 3
[AutoChessScene_436578261991426] Valid player: 90000074113, Health: 3
[AutoChessScene_436578261991426] Valid player: 90000068707, Health: 3
[AutoChessScene_436578261991426] No instance found for player 10102021302 when saving board data
[AutoChessScene_436578261991426] No instance found for player 90000091138 when saving board data
[AutoChessScene_436578261991426] No instance found for player 90000074113 when saving board data
[AutoChessScene_436578261991426] No instance found for player 90000068707 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000091138 vs Player 90000068707
[OpponentPairManager] Random pair: Player 90000074113 vs Player 10102021302
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_436578261991426] Created 4 opponent pairs
[PlayerManager_436578261991426] Set player opponents, count: 4
[BattleInstanceManager] Created instance 436578261991426_1 for active players 90000091138 vs 90000068707
[CheckerBoard_436578261991426] Cleared checkerboard
[CheckerBoard_436578261991426] Initialized
[BattleInstance] 436578261991426_1 created with players: 90000091138, 90000068707
[BattleInstanceManager] Created instance 436578261991426_2 for active players 90000074113 vs 10102021302
[CheckerBoard_436578261991426] Cleared checkerboard
[CheckerBoard_436578261991426] Initialized
[BattleInstance] 436578261991426_2 created with players: 90000074113, 10102021302
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_436578261991426] Cleaned orphaned entities for player 10102021302
[AutoChessScene_436578261991426] Cleaned orphaned entities for player 90000091138
[AutoChessScene_436578261991426] Cleaned orphaned entities for player 90000074113
[AutoChessScene_436578261991426] Cleaned orphaned entities for player 90000068707
[PlayerManager_436578261991426] Reset all players ready status
[AutoChessScene_436578261991426] Round started with 2 battle instances
[AutoChessScene_436578261991426] Generating 5 heroes for all players in round 1
[CheckerBoard_436578261991426] Placed entity 1 at position (9, 6)
[CheckerBoard_436578261991426] Created entity ID:1 ConfigID:101 StarLevel:1 at (9, 6) for player 10102021302
[CheckerBoard_436578261991426] Placed entity 2 at position (9, 5)
[CheckerBoard_436578261991426] Created entity ID:2 ConfigID:101 StarLevel:1 at (9, 5) for player 10102021302
[CheckerBoard_436578261991426] Placed entity 3 at position (6, 3)
[CheckerBoard_436578261991426] Created entity ID:3 ConfigID:102 StarLevel:1 at (6, 3) for player 10102021302
[CheckerBoard_436578261991426] Placed entity 4 at position (6, 2)
[CheckerBoard_436578261991426] Created entity ID:4 ConfigID:101 StarLevel:1 at (6, 2) for player 10102021302
[CheckerBoard_436578261991426] Placed entity 5 at position (10, 3)
[CheckerBoard_436578261991426] Created entity ID:5 ConfigID:101 StarLevel:1 at (10, 3) for player 10102021302
[CheckerBoard_436578261991426] CheckTimes limit (4) reached for 1 hero types for player 10102021302
[CheckerBoard_436578261991426] Generated 5/5 heroes for player 10102021302: 5 placed, 0 in temporary slots
[CheckerBoard_436578261991426] Generated 5 heroes for player 10102021302 in Enemy area
[AutoChessScene_436578261991426] Generated 5 heroes for player 10102021302: 5 placed on board, 0 in temporary slots
[CheckerBoard_436578261991426] Placed entity 1 at position (5, 6)
[CheckerBoard_436578261991426] Created entity ID:1 ConfigID:103 StarLevel:1 at (5, 6) for player 90000091138
[CheckerBoard_436578261991426] Placed entity 2 at position (5, 5)
[CheckerBoard_436578261991426] Created entity ID:2 ConfigID:102 StarLevel:1 at (5, 5) for player 90000091138
[CheckerBoard_436578261991426] Placed entity 3 at position (2, 1)
[CheckerBoard_436578261991426] Created entity ID:3 ConfigID:103 StarLevel:1 at (2, 1) for player 90000091138
[CheckerBoard_436578261991426] Placed entity 4 at position (4, 1)
[CheckerBoard_436578261991426] Created entity ID:4 ConfigID:102 StarLevel:1 at (4, 1) for player 90000091138
[CheckerBoard_436578261991426] Placed entity 5 at position (3, 5)
[CheckerBoard_436578261991426] Created entity ID:5 ConfigID:101 StarLevel:1 at (3, 5) for player 90000091138
[CheckerBoard_436578261991426] Generated 5/5 heroes for player 90000091138: 5 placed, 0 in temporary slots
[CheckerBoard_436578261991426] Generated 5 heroes for player 90000091138 in My area
[AutoChessScene_436578261991426] Generated 5 heroes for player 90000091138: 5 placed on board, 0 in temporary slots
[CheckerBoard_436578261991426] Placed entity 6 at position (3, 6)
[CheckerBoard_436578261991426] Created entity ID:6 ConfigID:101 StarLevel:1 at (3, 6) for player 90000074113
[CheckerBoard_436578261991426] Placed entity 7 at position (5, 3)
[CheckerBoard_436578261991426] Created entity ID:7 ConfigID:102 StarLevel:1 at (5, 3) for player 90000074113
[CheckerBoard_436578261991426] Placed entity 8 at position (5, 2)
[CheckerBoard_436578261991426] Created entity ID:8 ConfigID:101 StarLevel:1 at (5, 2) for player 90000074113
[CheckerBoard_436578261991426] Placed entity 9 at position (4, 1)
[CheckerBoard_436578261991426] Created entity ID:9 ConfigID:102 StarLevel:1 at (4, 1) for player 90000074113
[CheckerBoard_436578261991426] Placed entity 10 at position (3, 4)
[CheckerBoard_436578261991426] Created entity ID:10 ConfigID:102 StarLevel:1 at (3, 4) for player 90000074113
[CheckerBoard_436578261991426] Generated 5/5 heroes for player 90000074113: 5 placed, 0 in temporary slots
[CheckerBoard_436578261991426] Generated 5 heroes for player 90000074113 in My area
[AutoChessScene_436578261991426] Generated 5 heroes for player 90000074113: 5 placed on board, 0 in temporary slots
[CheckerBoard_436578261991426] Placed entity 6 at position (8, 5)
[CheckerBoard_436578261991426] Created entity ID:6 ConfigID:103 StarLevel:1 at (8, 5) for player 90000068707
[CheckerBoard_436578261991426] Placed entity 7 at position (10, 3)
[CheckerBoard_436578261991426] Created entity ID:7 ConfigID:102 StarLevel:1 at (10, 3) for player 90000068707
[CheckerBoard_436578261991426] Placed entity 8 at position (8, 4)
[CheckerBoard_436578261991426] Created entity ID:8 ConfigID:103 StarLevel:1 at (8, 4) for player 90000068707
[CheckerBoard_436578261991426] Placed entity 9 at position (6, 2)
[CheckerBoard_436578261991426] Created entity ID:9 ConfigID:101 StarLevel:1 at (6, 2) for player 90000068707
[CheckerBoard_436578261991426] Placed entity 10 at position (8, 6)
[CheckerBoard_436578261991426] Created entity ID:10 ConfigID:102 StarLevel:1 at (8, 6) for player 90000068707
[CheckerBoard_436578261991426] Generated 5/5 heroes for player 90000068707: 5 placed, 0 in temporary slots
[CheckerBoard_436578261991426] Generated 5 heroes for player 90000068707 in Enemy area
[AutoChessScene_436578261991426] Generated 5 heroes for player 90000068707: 5 placed on board, 0 in temporary slots
[AutoChessScene_436578261991426] Player status: Total=4, Active=4
[AutoChessScene_436578261991426] Player 10102021302: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_436578261991426] Player 90000091138: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_436578261991426] Player 90000074113: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_436578261991426] Player 90000068707: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_436578261991426] Sending RoundStart notifications to 4 active players...
[AutoChessScene_436578261991426] RoundStart board data: player:90000074113 heroes:5
[AutoChessScene_436578261991426] RoundStart board data: player:10102021302 heroes:5
[AutoChessScene_436578261991426] Sending RoundStart to Player 10102021302 on GameServer 10102
[AutoChessScene_436578261991426] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_436578261991426] RoundStart board data: player:90000091138 heroes:5
[AutoChessScene_436578261991426] RoundStart board data: player:90000068707 heroes:5
[AutoChessScene_436578261991426] Sending RoundStart to Player 90000091138 on GameServer 10102
[AutoChessScene_436578261991426] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_436578261991426] RoundStart board data: player:90000074113 heroes:5
[AutoChessScene_436578261991426] RoundStart board data: player:10102021302 heroes:5
[AutoChessScene_436578261991426] Sending RoundStart to Player 90000074113 on GameServer 10102
[AutoChessScene_436578261991426] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_436578261991426] RoundStart board data: player:90000091138 heroes:5
[AutoChessScene_436578261991426] RoundStart board data: player:90000068707 heroes:5
[AutoChessScene_436578261991426] Sending RoundStart to Player 90000068707 on GameServer 10102
[AutoChessScene_436578261991426] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_436578261991426] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 436578261991426 state to StateRoundStart
[AutoChessScene_436578261991426] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_436578261991426] BattleStateChangedEvent published successfully
[BattleStateManager_436578261991426] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_436578261991426] Battle state machine started successfully for battle 436578261991426
[AutoChessScene_436578261991426] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleStateManager_436578261991426] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_436578261991426] Preparation phase started
[PlayerManager_436578261991426] Player 90000091138 ready status set to True
[PlayerManager_436578261991426] Player 90000074113 ready status set to True
[PlayerManager_436578261991426] Player 90000068707 ready status set to True
[AutoChessScene_436578261991426] Auto-ready 3 additional bots
[AutoChessScene_436578261991426] Free operation phase started
[BattleService] Updated battle 436578261991426 state to StatePreparation
[AutoChessScene_436578261991426] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_436578261991426] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_436578261991426] MergeHero operation: Player 10102021302, From GridID 52 → To GridID 54
[AutoChessScene_436578261991426] MergeHero with 1 move operations:
[AutoChessScene_436578261991426] - Move 1: GridID 53 → 52
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] LeaveBattle request received from player 10102021302
[BattleService] Player 10102021302 logout, cleaning up battle 436578261991426
[BattleService] Only one real player in battle 436578261991426, cleaning up entire battle
[BattleService] Starting cleanup for battle 436578261991426
[BattleService] Removed battle state for 436578261991426
[CheckerBoard_436578261991426] Cleared all entities
[BuffManager_436578261991426] Cleared all buffs
[AutoChessScene_436578261991426] Scene resources disposed
[SceneManager] Removed AutoChessScene 436578261991426 from thread management
[BattleService] Cleaned up scene for battle 436578261991426
[BattleService] Cleanup completed for battle 436578261991426
[BattleService] LeaveBattle completed successfully for player 10102021302
[NatsServer] Received publish message on subject: /30001/natsrpc.BattleService/LeaveBattle, no reply needed
