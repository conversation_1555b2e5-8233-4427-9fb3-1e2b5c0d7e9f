[BattleService] Player 10102021302 (勤奋的.矿工) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000050808 (AI_勤奋的.矿工) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000037577 (AI_勤奋的.矿工) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000088474 (AI_勤奋的.矿工) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleStateManager_436274301894662] Initialized
[PlayerManager_436274301894662] Player 10102021302 (勤奋的.矿工) Trophy 1 Health 3
[PlayerManager_436274301894662] Player 90000050808 (AI_勤奋的.矿工) Trophy 1 Health 3
[PlayerManager_436274301894662] Player 90000037577 (AI_勤奋的.矿工) Trophy 1 Health 3
[PlayerManager_436274301894662] Player 90000088474 (AI_勤奋的.矿工) Trophy 1 Health 3
[PlayerManager_436274301894662] Initialized with 4 players
[BattleInstanceManager] Initialized for battle 436274301894662
[OpponentPairManager] Initialized for battle 436274301894662
[BuffManager_436274301894662] Initialized
[CheckerBoard_436274301894662] Cleared checkerboard
[CheckerBoard_436274301894662] Initialized
[AutoChessScene_436274301894662] Event handlers registered
[AutoChessScene_436274301894662] Battle 436274301894662 initialized with 4 players, waiting for all players to enter
[SceneManager] Added AutoChessScene 436274301894662 to thread management
[BattleService] Battle 436274301894662 created successfully with 4 players
[BattleService] Battle 436274301894662 is now waiting for all players to call EnterBattle RPC
[BattleService] Current battle state: StateNone (waiting for player entry)
[BattleService] Players expected to enter: [10102021302, 90000050808, 90000037577, 90000088474]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10102021302 is real player, others are bots =====
[BattleService] Auto-entered bot player 90000050808 immediately
[BattleService] Auto-entered bot player 90000037577 immediately
[BattleService] Auto-entered bot player 90000088474 immediately
[BattleService] Battle 436274301894662 bots auto-entered: 3/4 players ready
[BattleService] Status: 1 waiting (3/4 entered), 0 active
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021302 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021302 entered battle 436274301894662 (initial), current count: 4
[BattleService] All 4 players entered battle 436274301894662, starting battle state machine
[BattleService] Entered players: [90000050808, 90000037577, 90000088474, 10102021302]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 436274301894662
[AutoChessScene_436274301894662] StartBattleStateMachine() called for battle 436274301894662
[AutoChessScene_436274301894662] BattleStateManager is ready, starting first round...
[AutoChessScene_436274301894662] Current state before starting: StateNone
[BattleStateManager_436274301894662] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_436274301894662] Round 1 has buff selection: False
[BattleStateManager_436274301894662] Publishing RoundStartedEvent for round 1
[AutoChessScene_436274301894662] Round 1 started
[BattleStateManager_436274301894662] Setting state to StateRoundStart for round 1
[BattleStateManager_436274301894662] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_436274301894662] HandleRoundStart: 4 active players
[AutoChessScene_436274301894662] Valid player: 10102021302, Health: 3
[AutoChessScene_436274301894662] Valid player: 90000050808, Health: 3
[AutoChessScene_436274301894662] Valid player: 90000037577, Health: 3
[AutoChessScene_436274301894662] Valid player: 90000088474, Health: 3
[AutoChessScene_436274301894662] No instance found for player 10102021302 when saving board data
[AutoChessScene_436274301894662] No instance found for player 90000050808 when saving board data
[AutoChessScene_436274301894662] No instance found for player 90000037577 when saving board data
[AutoChessScene_436274301894662] No instance found for player 90000088474 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000088474 vs Player 90000037577
[OpponentPairManager] Random pair: Player 90000050808 vs Player 10102021302
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_436274301894662] Created 4 opponent pairs
[PlayerManager_436274301894662] Set player opponents, count: 4
[BattleInstanceManager] Created instance 436274301894662_1 for active players 90000088474 vs 90000037577
[CheckerBoard_436274301894662] Cleared checkerboard
[CheckerBoard_436274301894662] Initialized
[BattleInstance] 436274301894662_1 created with players: 90000088474, 90000037577
[BattleInstanceManager] Created instance 436274301894662_2 for active players 90000050808 vs 10102021302
[CheckerBoard_436274301894662] Cleared checkerboard
[CheckerBoard_436274301894662] Initialized
[BattleInstance] 436274301894662_2 created with players: 90000050808, 10102021302
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_436274301894662] Cleaned orphaned entities for player 10102021302
[AutoChessScene_436274301894662] Cleaned orphaned entities for player 90000050808
[AutoChessScene_436274301894662] Cleaned orphaned entities for player 90000037577
[AutoChessScene_436274301894662] Cleaned orphaned entities for player 90000088474
[PlayerManager_436274301894662] Reset all players ready status
[AutoChessScene_436274301894662] Round started with 2 battle instances
[AutoChessScene_436274301894662] Generating 5 heroes for all players in round 1
[CheckerBoard_436274301894662] Placed entity 1 at position (10, 6)
[CheckerBoard_436274301894662] Created entity ID:1 ConfigID:102 StarLevel:1 at (10, 6) for player 10102021302
[CheckerBoard_436274301894662] Placed entity 2 at position (6, 2)
[CheckerBoard_436274301894662] Created entity ID:2 ConfigID:102 StarLevel:1 at (6, 2) for player 10102021302
[CheckerBoard_436274301894662] Placed entity 3 at position (8, 4)
[CheckerBoard_436274301894662] Created entity ID:3 ConfigID:101 StarLevel:1 at (8, 4) for player 10102021302
[CheckerBoard_436274301894662] Placed entity 4 at position (7, 3)
[CheckerBoard_436274301894662] Created entity ID:4 ConfigID:103 StarLevel:1 at (7, 3) for player 10102021302
[CheckerBoard_436274301894662] Placed entity 5 at position (6, 3)
[CheckerBoard_436274301894662] Created entity ID:5 ConfigID:101 StarLevel:1 at (6, 3) for player 10102021302
[CheckerBoard_436274301894662] Generated 5/5 heroes for player 10102021302: 5 placed, 0 in temporary slots
[CheckerBoard_436274301894662] Generated 5 heroes for player 10102021302 in Enemy area
[AutoChessScene_436274301894662] Generated 5 heroes for player 10102021302: 5 placed on board, 0 in temporary slots
[CheckerBoard_436274301894662] Placed entity 6 at position (4, 1)
[CheckerBoard_436274301894662] Created entity ID:6 ConfigID:102 StarLevel:1 at (4, 1) for player 90000050808
[CheckerBoard_436274301894662] Placed entity 7 at position (3, 4)
[CheckerBoard_436274301894662] Created entity ID:7 ConfigID:103 StarLevel:1 at (3, 4) for player 90000050808
[CheckerBoard_436274301894662] Placed entity 8 at position (5, 6)
[CheckerBoard_436274301894662] Created entity ID:8 ConfigID:103 StarLevel:1 at (5, 6) for player 90000050808
[CheckerBoard_436274301894662] Placed entity 9 at position (4, 6)
[CheckerBoard_436274301894662] Created entity ID:9 ConfigID:102 StarLevel:1 at (4, 6) for player 90000050808
[CheckerBoard_436274301894662] Placed entity 10 at position (4, 4)
[CheckerBoard_436274301894662] Created entity ID:10 ConfigID:101 StarLevel:1 at (4, 4) for player 90000050808
[CheckerBoard_436274301894662] Generated 5/5 heroes for player 90000050808: 5 placed, 0 in temporary slots
[CheckerBoard_436274301894662] Generated 5 heroes for player 90000050808 in My area
[AutoChessScene_436274301894662] Generated 5 heroes for player 90000050808: 5 placed on board, 0 in temporary slots
[CheckerBoard_436274301894662] Placed entity 1 at position (9, 5)
[CheckerBoard_436274301894662] Created entity ID:1 ConfigID:102 StarLevel:1 at (9, 5) for player 90000037577
[CheckerBoard_436274301894662] Placed entity 2 at position (10, 1)
[CheckerBoard_436274301894662] Created entity ID:2 ConfigID:102 StarLevel:1 at (10, 1) for player 90000037577
[CheckerBoard_436274301894662] Placed entity 3 at position (9, 1)
[CheckerBoard_436274301894662] Created entity ID:3 ConfigID:103 StarLevel:1 at (9, 1) for player 90000037577
[CheckerBoard_436274301894662] Placed entity 4 at position (6, 2)
[CheckerBoard_436274301894662] Created entity ID:4 ConfigID:101 StarLevel:1 at (6, 2) for player 90000037577
[CheckerBoard_436274301894662] Placed entity 5 at position (6, 1)
[CheckerBoard_436274301894662] Created entity ID:5 ConfigID:102 StarLevel:1 at (6, 1) for player 90000037577
[CheckerBoard_436274301894662] Generated 5/5 heroes for player 90000037577: 5 placed, 0 in temporary slots
[CheckerBoard_436274301894662] Generated 5 heroes for player 90000037577 in Enemy area
[AutoChessScene_436274301894662] Generated 5 heroes for player 90000037577: 5 placed on board, 0 in temporary slots
[CheckerBoard_436274301894662] Placed entity 6 at position (2, 4)
[CheckerBoard_436274301894662] Created entity ID:6 ConfigID:103 StarLevel:1 at (2, 4) for player 90000088474
[CheckerBoard_436274301894662] Placed entity 7 at position (1, 4)
[CheckerBoard_436274301894662] Created entity ID:7 ConfigID:102 StarLevel:1 at (1, 4) for player 90000088474
[CheckerBoard_436274301894662] Placed entity 8 at position (5, 4)
[CheckerBoard_436274301894662] Created entity ID:8 ConfigID:101 StarLevel:1 at (5, 4) for player 90000088474
[CheckerBoard_436274301894662] Placed entity 9 at position (4, 2)
[CheckerBoard_436274301894662] Created entity ID:9 ConfigID:101 StarLevel:1 at (4, 2) for player 90000088474
[CheckerBoard_436274301894662] Placed entity 10 at position (3, 4)
[CheckerBoard_436274301894662] Created entity ID:10 ConfigID:103 StarLevel:1 at (3, 4) for player 90000088474
[CheckerBoard_436274301894662] Generated 5/5 heroes for player 90000088474: 5 placed, 0 in temporary slots
[CheckerBoard_436274301894662] Generated 5 heroes for player 90000088474 in My area
[AutoChessScene_436274301894662] Generated 5 heroes for player 90000088474: 5 placed on board, 0 in temporary slots
[AutoChessScene_436274301894662] Player status: Total=4, Active=4
[AutoChessScene_436274301894662] Player 10102021302: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_436274301894662] Player 90000050808: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_436274301894662] Player 90000037577: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_436274301894662] Player 90000088474: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_436274301894662] Sending RoundStart notifications to 4 active players...
[AutoChessScene_436274301894662] RoundStart board data: player:90000050808 heroes:5
[AutoChessScene_436274301894662] RoundStart board data: player:10102021302 heroes:5
[AutoChessScene_436274301894662] Sending RoundStart to Player 10102021302 on GameServer 10102
[AutoChessScene_436274301894662] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_436274301894662] RoundStart board data: player:90000050808 heroes:5
[AutoChessScene_436274301894662] RoundStart board data: player:10102021302 heroes:5
[AutoChessScene_436274301894662] Sending RoundStart to Player 90000050808 on GameServer 10102
[AutoChessScene_436274301894662] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_436274301894662] RoundStart board data: player:90000088474 heroes:5
[AutoChessScene_436274301894662] RoundStart board data: player:90000037577 heroes:5
[AutoChessScene_436274301894662] Sending RoundStart to Player 90000037577 on GameServer 10102
[AutoChessScene_436274301894662] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_436274301894662] RoundStart board data: player:90000088474 heroes:5
[AutoChessScene_436274301894662] RoundStart board data: player:90000037577 heroes:5
[AutoChessScene_436274301894662] Sending RoundStart to Player 90000088474 on GameServer 10102
[AutoChessScene_436274301894662] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_436274301894662] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 436274301894662 state to StateRoundStart
[AutoChessScene_436274301894662] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_436274301894662] BattleStateChangedEvent published successfully
[BattleStateManager_436274301894662] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_436274301894662] Battle state machine started successfully for battle 436274301894662
[AutoChessScene_436274301894662] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleStateManager_436274301894662] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_436274301894662] Preparation phase started
[PlayerManager_436274301894662] Player 90000050808 ready status set to True
[PlayerManager_436274301894662] Player 90000037577 ready status set to True
[PlayerManager_436274301894662] Player 90000088474 ready status set to True
[AutoChessScene_436274301894662] Auto-ready 3 additional bots
[AutoChessScene_436274301894662] Free operation phase started
[BattleService] Updated battle 436274301894662 state to StatePreparation
[AutoChessScene_436274301894662] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_436274301894662] BattleStateChangedEvent published successfully
[AutoChessScene_436274301894662] MergeHero operation: Player 10102021302, From GridID 24 → To GridID 19
[BattleInstance] 436274301894662_2 Player 10102021302 attempted to operate entity 9 owned by 90000050808 at GridID 24
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_436274301894662] Battle timeout occurred in state StatePreparation
[AutoChessScene_436274301894662] Preparation timeout - force ready all unready players
[PlayerManager_436274301894662] Player 10102021302 ready status set to True
[PlayerManager_436274301894662] All players are ready!
[AutoChessScene_436274301894662] All players are ready, transitioning to next state
[BattleStateManager_436274301894662] State: StatePreparation -> StateBattleStarting (R1, 1000ms)
[AutoChessScene_436274301894662] Applying battle start buffs for all players
[AutoChessScene_436274301894662] Camp info for player 90000050808: 5 heroes added
[AutoChessScene_436274301894662] Camp info for player 10102021302: 5 heroes added
[AutoChessScene_436274301894662] Created RoundBattleStart request for player 10102021302, Team order: [90000050808, 10102021302], total GridIDs used: 10
[AutoChessScene_436274301894662] Sent RoundBattleStart to Player 10102021302 vs Opponent 90000050808 with 2 teams
[AutoChessScene_436274301894662] Camp info for player 90000050808: 5 heroes added
[AutoChessScene_436274301894662] Camp info for player 10102021302: 5 heroes added
[AutoChessScene_436274301894662] Created RoundBattleStart request for player 90000050808, Team order: [90000050808, 10102021302], total GridIDs used: 10
[AutoChessScene_436274301894662] Sent RoundBattleStart to Player 90000050808 vs Opponent 10102021302 with 2 teams
[AutoChessScene_436274301894662] Camp info for player 90000088474: 5 heroes added
[AutoChessScene_436274301894662] Camp info for player 90000037577: 5 heroes added
[AutoChessScene_436274301894662] Created RoundBattleStart request for player 90000037577, Team order: [90000088474, 90000037577], total GridIDs used: 10
[AutoChessScene_436274301894662] Sent RoundBattleStart to Player 90000037577 vs Opponent 90000088474 with 2 teams
[AutoChessScene_436274301894662] Camp info for player 90000088474: 5 heroes added
[AutoChessScene_436274301894662] Camp info for player 90000037577: 5 heroes added
[AutoChessScene_436274301894662] Created RoundBattleStart request for player 90000088474, Team order: [90000088474, 90000037577], total GridIDs used: 10
[AutoChessScene_436274301894662] Sent RoundBattleStart to Player 90000088474 vs Opponent 90000037577 with 2 teams
[AutoChessScene_436274301894662] Sent RoundBattleStart notifications with seed: 732819227
[BattleService] Updated battle 436274301894662 state to StateBattleStarting
[AutoChessScene_436274301894662] State change sent to GameServer: StatePreparation -> StateBattleStarting (R1)
[BattleStateManager_436274301894662] BattleStateChangedEvent published successfully
[AutoChessScene_436274301894662] Force ready for player 10102021302 due to preparation timeout
[BattleStateManager_436274301894662] Published BattleTimeoutEvent for state StateBattleStarting
[BattleStateManager_436274301894662] State: StateBattleStarting -> StateBattleInProgress (R1, 65000ms)
[AutoChessScene_436274301894662] Starting all battle instances
[BattleInstance] 436274301894662_1 battle started
[BattleInstance] 436274301894662_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_436274301894662] Bot 90000050808 vs real player 10102021302, waiting for real player result
[AutoChessScene_436274301894662] Auto EndBattle for bot 90000037577 vs bot 90000088474, random result: bot 90000037577 wins = True
[AutoChessScene_436274301894662] Player 90000037577 sent EndBattleReq (win: True), instance: 436274301894662_1
[AutoChessScene_436274301894662] Waiting for opponent 90000088474 to send EndBattleReq for instance 436274301894662_1
[AutoChessScene_436274301894662] Player 90000088474 sent EndBattleReq (win: False), instance: 436274301894662_1
[BattleInstance] 436274301894662_1 battle finished, winner: 90000037577, loser: 90000088474
[AutoChessScene_436274301894662] Battle instance 436274301894662_1 completed: Winner 90000037577, Loser 90000088474
[AutoChessScene_436274301894662] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 436274301894662 state to StateBattleInProgress
[AutoChessScene_436274301894662] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R1)
[BattleStateManager_436274301894662] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] LeaveBattle request received from player 10102021302
[BattleService] Player 10102021302 logout, cleaning up battle 436274301894662
[BattleService] Only one real player in battle 436274301894662, cleaning up entire battle
[BattleService] Starting cleanup for battle 436274301894662
[BattleService] Removed battle state for 436274301894662
[CheckerBoard_436274301894662] Cleared all entities
[BuffManager_436274301894662] Cleared all buffs
