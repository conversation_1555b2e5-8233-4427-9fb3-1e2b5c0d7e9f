using System;
using System.Collections.Generic;
using System.Linq;
using LiteFrame.Framework;
using BattleServer.Game;
using BattleServer.Game.Core;

namespace BattleServer.Game.Board
{
    /// <summary>
    /// 格子一致性验证结果
    /// </summary>
    public class GridConsistencyResult
    {
        /// <summary>
        /// 是否一致
        /// </summary>
        public bool IsConsistent { get; set; }

        /// <summary>
        /// 孤立实体数量（字典中有但格子中没有）
        /// </summary>
        public int OrphanedEntities { get; set; }

        /// <summary>
        /// 无效位置数量
        /// </summary>
        public int InvalidPositions { get; set; }

        /// <summary>
        /// 有效临时位实体数量
        /// </summary>
        public int ValidTemporaryEntities { get; set; }

        /// <summary>
        /// 无效临时位实体数量
        /// </summary>
        public int InvalidTemporaryEntities { get; set; }

        /// <summary>
        /// 无效状态数量
        /// </summary>
        public int InvalidStates { get; set; }

        /// <summary>
        /// 位置不匹配数量（格子中的实体位置与字典中记录的不一致）
        /// </summary>
        public int PositionMismatches { get; set; }

        /// <summary>
        /// 格子中有但字典中缺失的实体数量
        /// </summary>
        public int MissingFromDictionary { get; set; }

        /// <summary>
        /// 获取验证结果的摘要信息
        /// </summary>
        public string GetSummary()
        {
            if (IsConsistent)
            {
                return $"Grid consistency OK (ValidTemp: {ValidTemporaryEntities})";
            }

            var issues = new List<string>();
            if (OrphanedEntities > 0) issues.Add($"Orphaned: {OrphanedEntities}");
            if (InvalidPositions > 0) issues.Add($"InvalidPos: {InvalidPositions}");
            if (InvalidTemporaryEntities > 0) issues.Add($"InvalidTemp: {InvalidTemporaryEntities}");
            if (InvalidStates > 0) issues.Add($"InvalidState: {InvalidStates}");
            if (PositionMismatches > 0) issues.Add($"PosMismatch: {PositionMismatches}");
            if (MissingFromDictionary > 0) issues.Add($"MissingDict: {MissingFromDictionary}");

            return $"Grid consistency FAILED: {string.Join(", ", issues)}";
        }
    }

    /// <summary>
    /// 棋盘格子类
    /// </summary>
    public class BoardGrid
    {
        public int Row { get; }
        public int Column { get; }
        public Entity Entity { get; set; }

        public BoardGrid(int row, int column)
        {
            Row = row;
            Column = column;
            Entity = ReferencePool.Acquire<Entity>();
        }

        public bool IsEmpty => Entity.EntityId == 0;
        public bool HasEntity => !IsEmpty;
    }
    
    /// <summary>
    /// 实体操作事件参数
    /// </summary>
    public class EntityEventArgs : EventArgs
    {
        public Entity Entity { get; }
        public int Row { get; }
        public int Column { get; }
        public long PlayerId { get; }

        public EntityEventArgs(Entity entity, int row, int column, long playerId)
        {
            Entity = entity;
            Row = row;
            Column = column;
            PlayerId = playerId;
        }
    }

    /// <summary>
    /// 实体合并事件参数
    /// </summary>
    public class EntityMergeEventArgs : EventArgs
    {
        public Entity SourceEntity { get; }
        public Entity TargetEntity { get; }
        public int SourceRow { get; }
        public int SourceColumn { get; }
        public int TargetRow { get; }
        public int TargetColumn { get; }

        public EntityMergeEventArgs(Entity sourceEntity, Entity targetEntity, 
                                  int sourceRow, int sourceColumn, 
                                  int targetRow, int targetColumn)
        {
            SourceEntity = sourceEntity;
            TargetEntity = targetEntity;
            SourceRow = sourceRow;
            SourceColumn = sourceColumn;
            TargetRow = targetRow;
            TargetColumn = targetColumn;
        }
    }

    /// <summary>
    /// 棋盘系统 - 管理所有实体的创建、放置、移动和合并
    /// </summary>
    public class CheckerBoard : BattleComponentBase
    {
        // 存储格子的二维数组 [row, column]
        private readonly BoardGrid[,] _grids;

        // 当前棋盘上所有实体的列表
        private readonly Dictionary<int, Entity> _entities = new();

        // 临时位存储 - 按玩家ID分组存储
        private readonly Dictionary<long, Queue<Entity>> _temporarySlots = new();

        // 下一个实体ID
        private int _nextEntityId = 1;

        // 事件总线
        private BattleEventBus _eventBus;

        // 棋盘属性
        public int RowCount => BattleConfig.Board.RowCount;
        public int ColumnCount => BattleConfig.Board.ColumnCount;
        public int MyAreaStart => BattleConfig.Board.MyAreaStart;
        public int MyAreaEnd => BattleConfig.Board.MyAreaEnd;
        public int EnemyAreaStart => BattleConfig.Board.EnemyAreaStart;
        public int EnemyAreaEnd => BattleConfig.Board.EnemyAreaEnd;

        // 实体操作事件
        public event EventHandler<EntityEventArgs>? EntityCreated;
        public event EventHandler<EntityEventArgs>? EntityMoved;
        public event EventHandler<EntityMergeEventArgs>? EntityMerged;
        public event EventHandler<EntityEventArgs>? EntityRemoved;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventBus">事件总线</param>
        public CheckerBoard(BattleEventBus? eventBus = null)
        {
            // 初始化棋盘格子 (格子索引从1开始，为了和坐标系统一致)
            _grids = new BoardGrid[BattleConfig.Board.RowCount + 1, BattleConfig.Board.ColumnCount + 1];
            _eventBus = eventBus ?? new BattleEventBus();

            // 创建所有格子
            for (int row = 1; row <= BattleConfig.Board.RowCount; row++)
            {
                for (int col = 1; col <= BattleConfig.Board.ColumnCount; col++)
                {
                    _grids[row, col] = new BoardGrid(row, col);
                }
            }
        }

        protected override void OnInitialize()
        {
            Clear();
            Log.Info($"[{LogName}] Initialized");
        }

        protected override void OnClear()
        {
            // 释放所有实体
            foreach (var entity in _entities.Values)
            {
                ReferencePool.Release(entity);
            }
            _entities.Clear();

            // 清理所有格子上的实体
            for (int row = 1; row <= BattleConfig.Board.RowCount; row++)
            {
                for (int col = 1; col <= BattleConfig.Board.ColumnCount; col++)
                {
                    var grid = _grids[row, col];
                    if (grid.Entity.EntityId != 0)
                    {
                        ReferencePool.Release(grid.Entity);
                        grid.Entity = ReferencePool.Acquire<Entity>();
                    }
                }
            }

            _nextEntityId = 1;
            Log.Info($"[{LogName}] Cleared all entities");
        }
        
        /// <summary>
        /// 获取指定位置的格子
        /// </summary>
        private BoardGrid? GetGrid(int row, int column) =>
            IsValidPosition(row, column) ? _grids[row, column] : null;

        /// <summary>
        /// 检查指定位置是否有实体
        /// </summary>
        public bool HasEntityAt(int row, int column)
        {
            var grid = GetGrid(row, column);
            return grid?.HasEntity == true;
        }

        /// <summary>
        /// 获取指定位置的实体
        /// </summary>
        public Entity GetEntityAt(int row, int column)
        {
            var grid = GetGrid(row, column);
            return grid?.HasEntity == true ? grid.Entity : ReferencePool.Acquire<Entity>();
        }

        /// <summary>
        /// 检查位置是否有效
        /// </summary>
        public bool IsValidPosition(int row, int column) =>
            row >= 1 && row <= BattleConfig.Board.RowCount &&
            column >= 1 && column <= BattleConfig.Board.ColumnCount;
        
        /// <summary>
        /// 在指定位置放置实体（原子操作，确保数据一致性）
        /// </summary>
        /// <param name="entity">要放置的实体</param>
        /// <param name="row">目标行坐标</param>
        /// <param name="column">目标列坐标</param>
        /// <returns>放置是否成功</returns>
        private bool PlaceEntity(Entity entity, int row, int column)
        {
            if (!IsValidPosition(row, column))
            {
                Log.Warning($"[{LogName}] Cannot place entity {entity.EntityId} at invalid position ({row}, {column})");
                return false;
            }

            var targetGrid = _grids[row, column];

            // 确保目标格子为空
            if (targetGrid.Entity.EntityId != 0)
            {
                Log.Warning($"[{LogName}] Cannot place entity {entity.EntityId} at occupied position ({row}, {column}), existing entity: {targetGrid.Entity.EntityId}");
                return false;
            }

            // 如果实体已经在棋盘上的其他位置，先原子性地移除
            if (entity.GridX != 0 && entity.GridY != 0)
            {
                if (!RemoveEntityFromPosition(entity, entity.GridX, entity.GridY))
                {
                    Log.Warning($"[{LogName}] Failed to remove entity {entity.EntityId} from old position ({entity.GridX}, {entity.GridY})");
                    return false;
                }
            }

            // 原子性地放置实体：同时更新格子、实体坐标和字典
            targetGrid.Entity = entity;
            entity.GridX = row;
            entity.GridY = column;
            _entities[entity.EntityId] = entity;

            Log.Debug($"[{LogName}] Placed entity {entity.EntityId} at position ({row}, {column})");

            // 验证操作后的一致性
            ValidateAndLogConsistency($"PlaceEntity({entity.EntityId})");

            return true;
        }

        /// <summary>
        /// 从指定位置移除实体（原子操作）
        /// </summary>
        /// <param name="entity">要移除的实体</param>
        /// <param name="row">位置行坐标</param>
        /// <param name="column">位置列坐标</param>
        /// <returns>移除是否成功</returns>
        private bool RemoveEntityFromPosition(Entity entity, int row, int column)
        {
            if (!IsValidPosition(row, column))
            {
                return false;
            }

            var grid = GetGrid(row, column);
            if (grid == null || grid.Entity.EntityId != entity.EntityId)
            {
                Log.Warning($"[{LogName}] Entity {entity.EntityId} not found at expected position ({row}, {column})");
                return false;
            }

            // 原子性地清理：格子引用和实体坐标
            grid.Entity = ReferencePool.Acquire<Entity>();
            entity.GridX = 0;
            entity.GridY = 0;

            Log.Debug($"[{LogName}] Removed entity {entity.EntityId} from position ({row}, {column})");
            return true;
        }
        
        /// <summary>
        /// 创建新实体并放置到棋盘
        /// </summary>
        /// <param name="configId">实体配置ID</param>
        /// <param name="ownerId">所属玩家ID</param>
        /// <param name="row">行坐标</param>
        /// <param name="column">列坐标</param>
        /// <returns>创建的实体</returns>
        public Entity CreateAndPlaceEntity(int configId, long ownerId, int row, int column)
        {
            if (!IsValidPosition(row, column))
            {
                Log.Warning($"[{LogName}] Cannot create entity at invalid position: ({row}, {column})");
                return ReferencePool.Acquire<Entity>();
            }
            
            if (HasEntityAt(row, column))
            {
                var existingEntity = GetEntityAt(row, column);
                Log.Warning($"[{LogName}] Cannot create entity at occupied position: ({row}, {column}), existing entity ID:{existingEntity.EntityId} ConfigID:{existingEntity.ConfigId}");
                return ReferencePool.Acquire<Entity>();
            }
            
            var entity = ReferencePool.Acquire<Entity>();
            entity.Init(_nextEntityId++, configId, ownerId);
            
            if (PlaceEntity(entity, row, column))
            {
                // 触发实体创建事件
                EntityCreated?.Invoke(this, new EntityEventArgs(entity, row, column, ownerId));
                Log.Info($"[{LogName}] Created entity ID:{entity.EntityId} ConfigID:{configId} StarLevel:{entity.StarLevel} at ({row}, {column}) for player {ownerId}");
                return entity;
            }
            else
            {
                // 放置失败，释放实体
                ReferencePool.Release(entity);
                Log.Warning($"[{LogName}] Failed to place entity ConfigID:{configId} at ({row}, {column})");
                return ReferencePool.Acquire<Entity>();
            }
        }
        
        /// <summary>
        /// 移动实体到新位置（原子操作，确保数据一致性）
        /// 支持移动到空位置和调换位置
        /// </summary>
        /// <param name="fromRow">起始行</param>
        /// <param name="fromCol">起始列</param>
        /// <param name="toRow">目标行</param>
        /// <param name="toCol">目标列</param>
        /// <returns>移动是否成功</returns>
        public bool MoveEntity(int fromRow, int fromCol, int toRow, int toCol)
        {
            Log.Info($"[{LogName}] MoveEntity called: ({fromRow},{fromCol}) → ({toRow},{toCol})");

            if (!IsValidPosition(fromRow, fromCol) || !IsValidPosition(toRow, toCol))
            {
                Log.Warning($"[{LogName}] Invalid position for move operation: from ({fromRow}, {fromCol}) to ({toRow}, {toCol})");
                return false;
            }

            var fromGrid = _grids[fromRow, fromCol];
            var toGrid = _grids[toRow, toCol];

            Log.Info($"[{LogName}] Move entities: From EntityId={fromGrid.Entity.EntityId}, To EntityId={toGrid.Entity.EntityId}");

            // 验证移动条件
            if (fromGrid.Entity.EntityId == 0)
            {
                Log.Warning($"[{LogName}] Cannot move: source position ({fromRow}, {fromCol}) is empty");
                return false;
            }

            // 如果目标位置为空，执行简单移动
            if (toGrid.Entity.EntityId == 0)
            {
                // 获取要移动的实体
                var entity = fromGrid.Entity;

                Log.Info($"[{LogName}] Executing simple move: Entity {entity.EntityId} from ({fromRow},{fromCol}) to ({toRow},{toCol})");

                // 原子性移动：先清理源位置，再设置目标位置
                fromGrid.Entity = ReferencePool.Acquire<Entity>();
                toGrid.Entity = entity;
                entity.GridX = toRow;
                entity.GridY = toCol;

                // 更新实体字典引用（确保引用最新）
                _entities[entity.EntityId] = entity;

                // 触发实体移动事件
                EntityMoved?.Invoke(this, new EntityEventArgs(entity, toRow, toCol, entity.OwnerId));
                Log.Info($"[{LogName}] Simple move completed: Entity {entity.EntityId} now at ({toRow}, {toCol})");

                // 验证操作后的一致性
                ValidateAndLogConsistency($"MoveEntity({entity.EntityId})");

                return true;
            }
            else
            {
                // 目标位置有实体，执行调换操作
                Log.Info($"[{LogName}] Target position occupied, executing swap: Entity {fromGrid.Entity.EntityId} ↔ Entity {toGrid.Entity.EntityId}");
                bool swapResult = SwapEntities(fromRow, fromCol, toRow, toCol);
                Log.Info($"[{LogName}] Swap operation result: {swapResult}");
                return swapResult;
            }
        }
        
        /// <summary>
        /// 尝试合成实体 (fromRow,fromCol的实体合入toRow,toCol的实体)
        /// </summary>
        /// <param name="fromRow">源实体行</param>
        /// <param name="fromCol">源实体列</param>
        /// <param name="toRow">目标实体行</param>
        /// <param name="toCol">目标实体列</param>
        /// <returns>合成是否成功</returns>
        public bool TryMergeEntities(int fromRow, int fromCol, int toRow, int toCol)
        {
            if (!IsValidPosition(fromRow, fromCol) || !IsValidPosition(toRow, toCol))
            {
                Log.Warning($"[{LogName}] Invalid position for merge operation: from ({fromRow}, {fromCol}) to ({toRow}, {toCol})");
                return false;
            }
            
            var fromGrid = _grids[fromRow, fromCol];
            var toGrid = _grids[toRow, toCol];
            
            // 确保两个格子都有实体
            if (fromGrid.Entity.EntityId == 0 || toGrid.Entity.EntityId == 0)
            {
                Log.Warning($"[{LogName}] Cannot merge: one or both positions empty. From ({fromRow}, {fromCol}) to ({toRow}, {toCol})");
                return false;
            }
            
            var sourceEntity = fromGrid.Entity;
            var targetEntity = toGrid.Entity;
            
            // 确保实体属于同一玩家
            if (sourceEntity.OwnerId != targetEntity.OwnerId)
            {
                Log.Warning($"[{LogName}] Cannot merge: entities belong to different players");
                return false;
            }
            
            // 检查是否可以合成 (相同英雄ID和相同星级)
            if (sourceEntity.ConfigId == targetEntity.ConfigId && 
                sourceEntity.StarLevel == targetEntity.StarLevel &&
                targetEntity.StarLevel < 3)
            {
                // 保存旧星级便于记录
                int oldStarLevel = targetEntity.StarLevel;
                
                // 目标实体升星
                targetEntity.UpgradeStar();
                
                // 准备触发事件的参数
                var eventArgs = new EntityMergeEventArgs(
                    sourceEntity, 
                    targetEntity, 
                    fromRow, fromCol, 
                    toRow, toCol);
                
                // 移除源实体
                _entities.Remove(sourceEntity.EntityId);
                fromGrid.Entity = ReferencePool.Acquire<Entity>();

                // 触发合成事件
                EntityMerged?.Invoke(this, eventArgs);

                // 释放源实体资源
                ReferencePool.Release(sourceEntity);

                // 尝试从临时位填充空位
                TryFillFromTemporarySlots(targetEntity.OwnerId, fromRow, fromCol);

                Log.Info($"[{LogName}] Merged entity from ({fromRow}, {fromCol}) to ({toRow}, {toCol}). Star level increased from {oldStarLevel} to {targetEntity.StarLevel}");
                return true;
            }
            
            Log.Warning($"[{LogName}] Cannot merge: incompatible entities (ConfigId or StarLevel mismatch, or max star reached)");
            return false;
        }
        
        // 合并两个位置的实体
        public bool MergeEntity(int sourceRow, int sourceCol, int targetRow, int targetCol)
        {
            if (!IsValidPosition(sourceRow, sourceCol) || !IsValidPosition(targetRow, targetCol))
            {
                return false;
            }
            
            var sourceGrid = _grids[sourceRow, sourceCol];
            var targetGrid = _grids[targetRow, targetCol];
            
            if (sourceGrid.Entity.EntityId == 0 || targetGrid.Entity.EntityId == 0)
            {
                return false; // 源或目标没有有效实体
            }
            
            // 提升目标实体星级
            targetGrid.Entity.StarLevel++;
            
            // 清理源格子实体
            RemoveEntity(sourceRow, sourceCol);
            
            return true;
        }
        
        /// <summary>
        /// 移除实体
        /// </summary>
        /// <param name="row">行坐标</param>
        /// <param name="column">列坐标</param>
        /// <returns>移除是否成功</returns>
        public bool RemoveEntity(int row, int column)
        {
            if (!IsValidPosition(row, column))
            {
                Log.Warning($"[{LogName}] Cannot remove entity from invalid position: ({row}, {column})");
                return false;
            }
            
            var grid = _grids[row, column];
            if (grid.Entity.EntityId != 0)
            {
                var entity = grid.Entity;
                _entities.Remove(entity.EntityId);
                grid.Entity = ReferencePool.Acquire<Entity>();
                
                // 触发实体移除事件
                EntityRemoved?.Invoke(this, new EntityEventArgs(entity, row, column, entity.OwnerId));
                Log.Info($"[{LogName}] Removed entity ID:{entity.EntityId} from ({row}, {column})");
                
                // 释放实体资源
                ReferencePool.Release(entity);
                return true;
            }
            
            Log.Warning($"[{LogName}] No entity to remove at ({row}, {column})");
            return false;
        }

        /// <summary>
        /// 交换两个位置的实体
        /// </summary>
        /// <param name="row1">第一个位置的行坐标</param>
        /// <param name="col1">第一个位置的列坐标</param>
        /// <param name="row2">第二个位置的行坐标</param>
        /// <param name="col2">第二个位置的列坐标</param>
        /// <returns>交换是否成功</returns>
        public bool SwapEntities(int row1, int col1, int row2, int col2)
        {
            Log.Info($"[{LogName}] SwapEntities called: ({row1},{col1}) ↔ ({row2},{col2})");

            if (!IsValidPosition(row1, col1) || !IsValidPosition(row2, col2))
            {
                Log.Warning($"[{LogName}] Cannot swap entities with invalid positions: ({row1}, {col1}) <-> ({row2}, {col2})");
                return false;
            }

            var grid1 = _grids[row1, col1];
            var grid2 = _grids[row2, col2];

            Log.Info($"[{LogName}] Swap entities: Position1 EntityId={grid1.Entity.EntityId}, Position2 EntityId={grid2.Entity.EntityId}");

            // 交换实体
            var tempEntity = grid1.Entity;
            grid1.Entity = grid2.Entity;
            grid2.Entity = tempEntity;

            // 更新实体位置信息
            if (grid1.Entity.EntityId != 0)
            {
                grid1.Entity.GridX = row1;
                grid1.Entity.GridY = col1;
                Log.Info($"[{LogName}] Updated Entity {grid1.Entity.EntityId} position to ({row1},{col1})");
            }

            if (grid2.Entity.EntityId != 0)
            {
                grid2.Entity.GridX = row2;
                grid2.Entity.GridY = col2;
                Log.Info($"[{LogName}] Updated Entity {grid2.Entity.EntityId} position to ({row2},{col2})");
            }

            Log.Info($"[{LogName}] Swap completed successfully: ({row1},{col1}) ↔ ({row2},{col2})");
            return true;
        }

        // 获取棋盘行数
        public int GetRowCount()
        {
            return RowCount;
        }
        
        // 获取棋盘列数
        public int GetColumnCount()
        {
            return ColumnCount;
        }
        
        /// <summary>
        /// 清理棋盘
        /// </summary>
        public new void Clear()
        {
            // 释放所有实体
            foreach (var entity in _entities.Values)
            {
                ReferencePool.Release(entity);
            }
            
            _entities.Clear();
            _nextEntityId = 1;
            
            // 重置所有格子
            for (int row = 1; row <= RowCount; row++)
            {
                for (int col = 1; col <= ColumnCount; col++)
                {
                    _grids[row, col].Entity = ReferencePool.Acquire<Entity>();
                }
            }
            
            Log.Info($"[{LogName}] Cleared checkerboard");
        }
        
        /// <summary>
        /// 从玩家阵容中生成英雄（遵循CheckTimes限制）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="lineup">玩家阵容</param>
        /// <param name="count">请求生成数量</param>
        /// <param name="isMyArea">是否放置在玩家区域</param>
        /// <returns>生成的英雄实体列表</returns>
        public List<Entity> GenerateHeroesFromLineup(long playerId, List<int> lineup, int count, bool isMyArea = true)
        {
            if (lineup == null || lineup.Count == 0)
            {
                Log.Error($"[{LogName}] Cannot generate heroes: lineup is empty for player {playerId}");
                return new List<Entity>();
            }
            
            // 创建随机器
            var random = new Random();

            // 生成英雄的列表
            var generatedEntities = new List<Entity>();

            // 获取单角色复选上限
            int checkTimes = BattleConfig.GameRules.GetCheckTimes();

            // 统计每个英雄已生成的数量
            var heroGeneratedCount = new Dictionary<int, int>();
            foreach (var heroId in lineup)
            {
                heroGeneratedCount[heroId] = 0;
            }

            // 选择放置区域
            int startRow = isMyArea ? MyAreaStart : EnemyAreaStart;
            int endRow = isMyArea ? MyAreaEnd : EnemyAreaEnd;

            // 记录可用位置
            var availablePositions = new List<(int Row, int Column)>();

            // 找出所有可放置的位置
            for (int row = startRow; row <= endRow; row++)
            {
                for (int col = 1; col <= ColumnCount; col++)
                {
                    if (!HasEntityAt(row, col))
                    {
                        availablePositions.Add((row, col));
                    }
                }
            }

            // 随机打乱可放置位置
            availablePositions = availablePositions.OrderBy(x => random.Next()).ToList();

            // 确保玩家有临时位队列
            if (!_temporarySlots.ContainsKey(playerId))
            {
                _temporarySlots[playerId] = new Queue<Entity>();
            }

            // 生成英雄，遵循CheckTimes限制
            int actualGeneratedCount = 0;
            for (int i = 0; i < count; i++)
            {
                // 找到还可以生成的英雄
                var availableHeroes = lineup.Where(heroId => heroGeneratedCount[heroId] < checkTimes).ToList();

                if (availableHeroes.Count == 0)
                {
                    // 所有英雄都已达到复选上限
                    Log.Info($"[{LogName}] All heroes reached CheckTimes limit ({checkTimes}) for player {playerId}. Generated {actualGeneratedCount}/{count} heroes.");
                    break;
                }

                // 随机选择一个可用英雄
                int heroConfigIdx = random.Next(availableHeroes.Count);
                int heroConfigId = availableHeroes[heroConfigIdx];

                // 增加该英雄的生成计数
                heroGeneratedCount[heroConfigId]++;

                Entity entity = null;

                // 如果有可用位置，直接放置到棋盘
                if (actualGeneratedCount < availablePositions.Count)
                {
                    var position = availablePositions[actualGeneratedCount];
                    entity = CreateAndPlaceEntity(heroConfigId, playerId, position.Row, position.Column);
                    if (entity.EntityId != 0)
                    {
                        generatedEntities.Add(entity);
                        actualGeneratedCount++;
                    }
                }
                else
                {
                    // 位置不足，放入临时位
                    entity = ReferencePool.Acquire<Entity>();
                    entity.Init(_nextEntityId++, heroConfigId, playerId);
                    _temporarySlots[playerId].Enqueue(entity);
                    _entities[entity.EntityId] = entity;
                    actualGeneratedCount++;

                    Log.Info($"[{LogName}] Hero {heroConfigId} placed in temporary slot for player {playerId}");
                }
            }

            int placedCount = generatedEntities.Count;
            int temporaryCount = actualGeneratedCount - placedCount;

            // 记录CheckTimes限制的影响
            var limitedHeroes = heroGeneratedCount.Where(kvp => kvp.Value >= checkTimes).ToList();
            if (limitedHeroes.Count > 0)
            {
                Log.Info($"[{LogName}] CheckTimes limit ({checkTimes}) reached for {limitedHeroes.Count} hero types for player {playerId}");
            }

            Log.Info($"[{LogName}] Generated {actualGeneratedCount}/{count} heroes for player {playerId}: {placedCount} placed, {temporaryCount} in temporary slots");
            
            Log.Info($"[{LogName}] Generated {generatedEntities.Count} heroes for player {playerId} in {(isMyArea ? "My" : "Enemy")} area");
            return generatedEntities;
        }

        /// <summary>
        /// 尝试从临时位填充指定位置
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="row">目标行</param>
        /// <param name="col">目标列</param>
        private void TryFillFromTemporarySlots(long playerId, int row, int col)
        {
            if (!_temporarySlots.ContainsKey(playerId) || _temporarySlots[playerId].Count == 0)
            {
                return;
            }

            if (!IsValidPosition(row, col) || HasEntityAt(row, col))
            {
                return;
            }

            var entity = _temporarySlots[playerId].Dequeue();
            if (PlaceEntity(entity, row, col))
            {
                Log.Info($"[{LogName}] Filled position ({row}, {col}) from temporary slot for player {playerId}, hero {entity.ConfigId}");
            }
            else
            {
                // 放置失败，重新放回临时位
                _temporarySlots[playerId].Enqueue(entity);
            }
        }

        /// <summary>
        /// 清空玩家的临时位（回合结束时调用）
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        public void ClearPlayerTemporarySlots(long playerId)
        {
            if (!_temporarySlots.ContainsKey(playerId))
            {
                return;
            }

            var temporaryQueue = _temporarySlots[playerId];
            while (temporaryQueue.Count > 0)
            {
                var entity = temporaryQueue.Dequeue();
                _entities.Remove(entity.EntityId);
                ReferencePool.Release(entity);
            }

            Log.Info($"[{LogName}] Cleared temporary slots for player {playerId}");
        }

        /// <summary>
        /// 获取玩家临时位中的英雄数量
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>临时位英雄数量</returns>
        public int GetTemporarySlotCount(long playerId)
        {
            return _temporarySlots.ContainsKey(playerId) ? _temporarySlots[playerId].Count : 0;
        }
        
        /// <summary>
        /// 检查玩家区域内是否还有英雄
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>是否有英雄</returns>
        public bool HasHeroesForPlayer(long playerId)
        {
            return GetPlayerEntities(playerId).Count > 0;
        }
        
        /// <summary>
        /// 获取玩家的所有有效实体（包括棋盘上的和临时位的）
        /// 只返回经过验证的、状态一致的实体
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>玩家的有效实体列表</returns>
        public List<Entity> GetPlayerEntities(long playerId)
        {
            var result = new List<Entity>();
            var orphanedEntities = new List<int>(); // 收集孤立实体ID，稍后清理

            foreach (var kvp in _entities)
            {
                var entity = kvp.Value;
                var entityId = kvp.Key;

                if (entity.OwnerId != playerId)
                    continue;

                // 验证棋盘上的实体
                if (entity.GridX > 0 && entity.GridY > 0)
                {
                    if (IsValidPosition(entity.GridX, entity.GridY))
                    {
                        var gridEntity = GetGrid(entity.GridX, entity.GridY)?.Entity;
                        if (gridEntity != null && gridEntity.EntityId == entity.EntityId)
                        {
                            // 实体在正确位置，状态一致
                            result.Add(entity);
                        }
                        else
                        {
                            // 孤立实体：字典中有但格子中没有或不匹配
                            orphanedEntities.Add(entityId);
                        }
                    }
                    else
                    {
                        // 实体位置无效
                        orphanedEntities.Add(entityId);
                    }
                }
                else if (entity.GridX == 0 && entity.GridY == 0)
                {
                    // 临时位实体：验证是否真的在临时位队列中
                    if (_temporarySlots.ContainsKey(playerId) && _temporarySlots[playerId].Contains(entity))
                    {
                        result.Add(entity);
                    }
                    else
                    {
                        orphanedEntities.Add(entityId);
                    }
                }
                else
                {
                    // 其他无效状态
                    orphanedEntities.Add(entityId);
                }
            }

            // 静默清理发现的孤立实体
            foreach (var entityId in orphanedEntities)
            {
                if (_entities.TryGetValue(entityId, out var orphanedEntity))
                {
                    _entities.Remove(entityId);
                    ReferencePool.Release(orphanedEntity);
                }
            }

            // 只在有清理时才记录日志，减少噪音
            if (orphanedEntities.Count > 0)
            {
                Log.Info($"[{LogName}] GetPlayerEntities for player {playerId}: cleaned {orphanedEntities.Count} orphaned entities, returned {result.Count} valid entities");
            }
            return result;
        }

        /// <summary>
        /// 验证格子一致性（轻量级验证机制）
        /// 检查实体字典和格子状态的一致性，返回验证结果
        /// </summary>
        /// <returns>验证结果，包含发现的问题数量</returns>
        public GridConsistencyResult ValidateGridConsistency()
        {
            var result = new GridConsistencyResult();

            // 验证实体字典中的实体是否在正确的格子位置
            foreach (var kvp in _entities)
            {
                var entity = kvp.Value;
                var entityId = kvp.Key;

                if (entity.GridX > 0 && entity.GridY > 0)
                {
                    // 棋盘上的实体
                    if (IsValidPosition(entity.GridX, entity.GridY))
                    {
                        var gridEntity = GetGrid(entity.GridX, entity.GridY)?.Entity;
                        if (gridEntity == null || gridEntity.EntityId != entity.EntityId)
                        {
                            result.OrphanedEntities++;
                        }
                    }
                    else
                    {
                        result.InvalidPositions++;
                    }
                }
                else if (entity.GridX == 0 && entity.GridY == 0)
                {
                    // 临时位实体
                    if (_temporarySlots.ContainsKey(entity.OwnerId) &&
                        _temporarySlots[entity.OwnerId].Contains(entity))
                    {
                        result.ValidTemporaryEntities++;
                    }
                    else
                    {
                        result.InvalidTemporaryEntities++;
                    }
                }
                else
                {
                    result.InvalidStates++;
                }
            }

            // 验证格子中的实体是否在实体字典中
            for (int row = 1; row <= RowCount; row++)
            {
                for (int col = 1; col <= ColumnCount; col++)
                {
                    var grid = GetGrid(row, col);
                    if (grid?.Entity != null && grid.Entity.EntityId != 0)
                    {
                        if (_entities.ContainsKey(grid.Entity.EntityId))
                        {
                            var dictEntity = _entities[grid.Entity.EntityId];
                            if (dictEntity.GridX != row || dictEntity.GridY != col)
                            {
                                result.PositionMismatches++;
                            }
                        }
                        else
                        {
                            result.MissingFromDictionary++;
                        }
                    }
                }
            }

            result.IsConsistent = (result.OrphanedEntities == 0 &&
                                 result.InvalidPositions == 0 &&
                                 result.InvalidTemporaryEntities == 0 &&
                                 result.InvalidStates == 0 &&
                                 result.PositionMismatches == 0 &&
                                 result.MissingFromDictionary == 0);

            return result;
        }

        /// <summary>
        /// 验证并记录格子一致性（在关键操作后调用）
        /// </summary>
        /// <param name="operationName">操作名称，用于日志记录</param>
        /// <param name="logOnSuccess">是否在验证成功时也记录日志</param>
        public void ValidateAndLogConsistency(string operationName, bool logOnSuccess = false)
        {
            var result = ValidateGridConsistency();

            if (!result.IsConsistent)
            {
                Log.Error($"[{LogName}] Grid consistency check FAILED after {operationName}: {result.GetSummary()}");
            }
            else if (logOnSuccess)
            {
                Log.Debug($"[{LogName}] Grid consistency check OK after {operationName}: {result.GetSummary()}");
            }
        }

        /// <summary>
        /// 清理无效的实体引用（轻量级版本，减少冗余检测）
        /// 注意：GetPlayerEntities方法已经包含了实时清理，此方法主要用于定期维护
        /// </summary>
        public void CleanupOrphanedEntities()
        {
            var orphanedEntityIds = new List<int>();

            foreach (var kvp in _entities)
            {
                var entity = kvp.Value;
                var entityId = kvp.Key;

                // 只检查棋盘上的实体，临时位实体由GetPlayerEntities处理
                if (entity.GridX > 0 && entity.GridY > 0)
                {
                    if (!IsValidPosition(entity.GridX, entity.GridY))
                    {
                        orphanedEntityIds.Add(entityId);
                    }
                    else
                    {
                        var gridEntity = GetGrid(entity.GridX, entity.GridY)?.Entity;
                        if (gridEntity == null || gridEntity.EntityId != entity.EntityId)
                        {
                            orphanedEntityIds.Add(entityId);
                        }
                    }
                }
            }

            // 静默清理孤立实体，减少日志噪音
            foreach (var entityId in orphanedEntityIds)
            {
                if (_entities.TryGetValue(entityId, out var entity))
                {
                    _entities.Remove(entityId);
                    ReferencePool.Release(entity);
                }
            }

            // 只在有清理时才记录日志
            if (orphanedEntityIds.Count > 0)
            {
                Log.Info($"[{LogName}] Cleaned {orphanedEntityIds.Count} orphaned entities during maintenance");
            }
        }

        /// <summary>
        /// 获取范围内的所有实体
        /// </summary>
        /// <param name="startRow">开始行</param>
        /// <param name="startCol">开始列</param>
        /// <param name="endRow">结束行</param>
        /// <param name="endCol">结束列</param>
        /// <returns>区域内的实体列表</returns>
        public List<Entity> GetEntitiesInArea(int startRow, int startCol, int endRow, int endCol)
        {
            List<Entity> result = new List<Entity>();
            
            for (int row = startRow; row <= endRow; row++)
            {
                for (int col = startCol; col <= endCol; col++)
                {
                    if (IsValidPosition(row, col))
                    {
                        var grid = _grids[row, col];
                        if (grid.Entity.EntityId != 0)
                        {
                            result.Add(grid.Entity);
                        }
                    }
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 获取玩家自己的区域实体
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>玩家区域的实体列表</returns>
        public List<Entity> GetEntitiesInMyArea(long playerId)
        {
            List<Entity> result = new List<Entity>();
            
            for (int row = MyAreaStart; row <= MyAreaEnd; row++)
            {
                for (int col = 1; col <= ColumnCount; col++)
                {
                    var grid = _grids[row, col];
                    if (grid.Entity.EntityId != 0 && grid.Entity.OwnerId == playerId)
                    {
                        result.Add(grid.Entity);
                    }
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 获取所有实体
        /// </summary>
        /// <returns>所有实体的复制列表</returns>
        public List<Entity> GetAllEntities()
        {
            return new List<Entity>(_entities.Values);
        }
    }
}
